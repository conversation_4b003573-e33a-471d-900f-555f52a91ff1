<?php

namespace App\Http\Controllers\Billing;

use Stripe\Coupon;
use Stripe\StripeClient;
use Illuminate\Http\Request;
use App\Classes\Plan\PlanClass;
use App\Models\Billing\Billing;
use App\Models\Organization\Plan;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Cashier\Subscription;
use Illuminate\Support\Facades\Auth;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\Organization;
use App\Classes\Billing\SubscriptionClass;
use App\Models\Billing\ShopifySubscription;
use Stripe\Exception\InvalidRequestException;

class BillingController extends Controller
{
    private $stripe;

    protected $subscriptionClass;

    public function __construct(SubscriptionClass $subscriptionClass)
    {
        $this->subscriptionClass = $subscriptionClass;
        $this->middleware(['auth', 'verified', 'activeOrganization']);
        $this->stripe = new StripeClient(env('STRIPE_SECRET'));
    }

    public function index()
    {
        $this->authorize('perform_billing', [\App\Models\Organization\OrganizationUserPermission::class, auth()->user()->organization_id]);
        try {
            $user = Auth::user();
            $organization = Organization::where('id', Auth::user()->organization_id)->firstOrFail();
            if (isset($organization->stripe_id)) {
                $subscription = Subscription::with('items')->where('organization_id', $organization->id)->first();
                $subscribedPlan = null;
                if ($subscription) {
                    if($organization->plan_handle){
                        $subscribedPlan = PlanClass::$plans->where('handle', $organization->plan_handle)->first();
                    }else{
                        if($subscription->stripe_price){
                            $subscribedPlan = PlanClass::$plans->where('stripe_monthly_id', $subscription->stripe_price)->first();
                        }else{
                            foreach ($subscription->items as $key => $item) {
                                $subscribedPlan = PlanClass::$plans->where('stripe_monthly_id', $item->stripe_price)->first();
                                if($subscribedPlan){
                                     break;
                                }
                            }
                        }
                    }

                    if (!$subscribedPlan) {
                        throw new \Exception('Subscribed Plan does not exist');
                    }
                    return redirect(route('subscription.info'));
                }
                return view('billing.buttons', compact('subscribedPlan', 'user'));
            } elseif (isset($user->shopify_shop_id)) {
                $plan_handle = ShopifySubscription::where('organization_id', $user->organization_id)->latest('id')->value('plan_handle');
                $subscribedPlan = null;
                if ($plan_handle) {
                    $subscribedPlan = PlanClass::$plans->where('handle', $plan_handle)->first();
                    if (!$subscribedPlan) {
                        throw new \Exception('Subscribed Plan does not exist');
                    }
                    return redirect(route('subscription.info'));
                }

                $organization = Organization::where('id', $user?->organization_id)->first();
                return view('billing.buttons', compact('user'));
            }
        } catch (\Exception $e) {
            return redirect(route("dashboard"))->withErrors(['main' => $e->getMessage()]);
        }
    }

    //Redirecting to billing portal
    public function billingPortal()
    {
        $org = Organization::where('id', Auth::user()->organization_id)->first();
        return $org->redirectToBillingPortal();
    }

    public function viewPlans()
    {
        $user = Auth::user();
        return view('billing.buttons', compact('user'));
    }

    public function viewSubscription(Request $request)
    {
        try {
            $organization = Organization::where('id', Auth::user()->organization_id)->first();

            $user = Auth::user();
            $handle = null;
            $subscription = null;
            if ($organization->stripe_id) {
                $handle  = $organization->plan_handle;
                $subscription = Subscription::where('organization_id', $user->organization_id)->latest('id')->first();
                if(!$handle && $subscription){
                    if($subscription->stripe_price){
                        $handle = PlanClass::$plans->firstWhere('stripe_monthly_id', $subscription?->stripe_price)?->handle;
                    }else{
                        foreach ($subscription->items as $key => $item) {
                            $handle = PlanClass::$plans->where('stripe_monthly_id', $item->stripe_price)->first()?->handle;
                            if($handle){
                                 break;
                            }
                        }
                    }
                }
                $subscriptionUrl = route('billing.portal');
            } elseif ($user->shopify_shop_id) {
                $subscription = ShopifySubscription::where('organization_id', $user->organization_id)->latest('id')->first();
                $handle = $subscription?->plan_handle;
                $shopify = ShopifyChannel::find($subscription->shopify_channel_id);
                $subscriptionUrl = "#";
                if ($shopify) { // Example shop domain
                    $shopName = str_replace(".myshopify.com", "", $shopify->shop);
                    $subscriptionUrl = "https://admin.shopify.com/store/" . $shopName . "/settings/billing";
                }
            }
            $plan = PlanClass::$plans->where('handle', $handle)->first();
            return view('billing.check_subscription1', compact('organization', 'subscriptionUrl', 'subscription', 'plan', 'user'));
        } catch (\Exception $e) {
            return redirect(route("dashboard"))->withErrors(['main' => $e->getMessage()]);
        }

    }

    public function Subscription(Request $request)
    {
        $validation = $this->subscriptionClass->set_data($request->all())->validation();

        if ($validation->fails()) {
            return redirect()->back()->withInput()->withErrors($validation->errors());
        } else {
            return (new SubscriptionClass)
                ->set_data($request->all())
                ->set_user(Auth::user())
                ->set_organization($request->organization_id)
                ->setMonthlyPlan($request->handle)
                ->subscription(
                    function ($error) {
                        return redirect()->back()->withErrors(["main" => $error]);
                        return redirect(route('dashboard'))->withErrors(["main" => $error]);
                    },
                    function ($success) {
                        if ($success) {
                            if (isset($success["url"])) {
                                return   $success["url"];
                            }
                            return redirect(route('dashboard'))->withSuccess($success["msg"]);
                        }
                    }
                );
        }
    }

    public function update_plan($plan_handle)
    {
        $var = $this->plan_id($plan_handle);
        $billing = new Billing();
        $billing->Subscription(Auth::user(), $var[1]);
        return redirect(route('dashboard'))->withSuccess('Plan upgraded successfully');
    }

    public function plan_id($plan_handle)
    {
        $plan = PlanClass::$plans->where('handle', $plan_handle)->first();
        return [$plan, $plan->stripe_monthly_id];
    }
}
