<?php

namespace App\Models\Channel;

use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\Product\Brand;
use App\Models\Product\Family;
use App\Classes\Plan\PlanClass;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Product\Version;
use App\Rules\is_hmac_verified;
use App\Jobs\FetchShopifyProduct;
use App\Models\Organization\File;
use App\Models\Organization\Plan;
use App\Models\Product\Attribute;
use App\Rules\ShopifyProductSync;
use App\Scopes\UserSpecificScope;
use App\Traits\Shopify\ShopifyAPI;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;
use App\Classes\Shopify\SyncMetafield;
use App\Models\Product\ProductVersion;
use App\Jobs\PreviousData\InventoryJob;
use App\Models\Product\AttributeFamily;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Session;
use App\Jobs\Shopify\UpdateInventoryJob;
use Illuminate\Support\Facades\Redirect;
use App\Models\Organization\Organization;
use App\Notifications\ApimioNotification;
use App\Rules\is_shopify_store_connected;
use Illuminate\Support\Facades\Validator;
use App\Models\Billing\ShopifySubscription;
use App\Models\Product\ProductShopifyMapping;
use App\Traits\Billing\RemainingProductLimit;
use Illuminate\Http\Client\HttpClientException;
use App\Models\Product\AttributeFamilyProductVersion;

class ShopifyChannel extends Model
{
    use ShopifyAPI, RemainingProductLimit;

    public $validation, $shopify_base_url;

    private
        $data,
        $shopify_channel = null,
        $product_count   = 0,
        $last_exception;


    private
        $shopify_api_key,
        $shopify_shared_secret,
        $scopes, $redirect_uri,
        $grant_options              = "offline",    // shopify offline online mode for users read documentation for information.
        $shopify_url,
        $product = null,
        $shopify_product_array = null;

    public static
        $API_VERSION                = "2023-04",    // api version of shopify currently used in app.
        $BILLING_TRIAL              = "0",         // trial in no. of days we are giving to users.
        $X_CAPPED_AMOUNT            = 10;           // x is the no. of times the amount is charged automatically by shopify.


    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
    }

    /**
     * Constructor for storing data and applying post logic
     * to organization structure.
     *
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        // set shopify credentials
        $this->shopify_shared_secret            = env("SHOPIFY_SHARED_SECRET");
        $this->shopify_api_key                  = env("SHOPIFY_API_KEY");
        $this->scopes                           = env("SHOPIFY_SCOPES");
        $this->redirect_uri                     = env("SHOPIFY_REDIRECT_URI");
        $this->grant_options                    = "offline";

        parent::__construct($attributes);
    }

    /*
     *
     *
     * */

    /**
     * Set data attribute.
     *
     *
     * @param  array
     * @return $this
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return array
     */
    public function get_data()
    {
        return $this->data;
    }

    public function find_channel_by_id($shopify_channels_id) {
        $this->shopify_channel = $this->find($shopify_channels_id);
        return $this;
    }

    public function find_channel_by_shopify_id($shopify_id) {
        $this->shopify_channel = $this->where("shop_id", $shopify_id)->get()->first()->toArray();
        return $this;
    }

    public function find_plan_by_id($plan_handle) {
        $this->plan = PlanClass::$plans->where("handle",$plan_handle)->first();
        return $this;
    }

    public function is_plan_price_is_zero() {
        $this->find_plan_by_id($this->data["plan_handle"]);

        return isset($this->plan->price_per_month) ? ($this->plan->price_per_month <= 0) : false;
    }

    public function get_shopify_channel() {
        return $this->shopify_channel;
    }

    public function get_access_token() {
        try {
            // Generate access token URL
            $response  = Http::retry(3, 100)->post("https://" . $this->data["shop"] . "/admin/oauth/access_token", [
                "client_id" => $this->shopify_api_key, // Your API key
                "client_secret" => $this->shopify_shared_secret, // Your app credentials (secret key)
                "code" => $this->data["code"] // Grab the access key from the URL
            ]);

            if($response->successful()) {
                $this->data["access_token"] = $response->json()["access_token"];
                return $this->data["access_token"];
            }
        }
        catch (\Exception $e) {
            Log::warning("Shopify error : access token not retrieved try again. Token " . $this->shopify_shared_secret);
            return null;
        }
    }

    public function bill($error_func) {
        $validation = $this->validation($this->bill_rules());
        if($validation->fails()) {
            return $error_func($validation->errors());
        }

        $this->find_channel_by_id($this->data["shopify_channel_id"]);
        $this->find_plan_by_id($this->data["plan_handle"]);

        $this->shopify_base_url         = "https://".$this->shopify_api_key.":".$this->shopify_channel["access_token"]."@".$this->shopify_channel["shop"]."/admin/api/".self::$API_VERSION."/";
        $response = Http::retry(1, 50)->post($this->shopify_base_url."recurring_application_charges.json", [
            "recurring_application_charge" => [
                "name" => $this->plan->name,
                "price" => $this->plan->price_per_month,
                "test" => env("SHOPIFY_TEST_ENV"),
                "trial_days" => self::$BILLING_TRIAL,
                "capped_amount" => ($this->plan->price_per_month*self::$X_CAPPED_AMOUNT),
                "return_url" => route('channel.shopify.bill.redirect'),
                "terms" => "$".$this->plan->price_per_month." for every additional retailer."
            ]
        ]);

        if($response->successful()) {
// dd($response->json());
            DB::table("shopify_subscriptions")->insert(
                [
                    "name"                              => $response->json()["recurring_application_charge"]["name"],
                    "shopify_channel_id"                => $this->shopify_channel["id"],
                    "recurring_application_charge_id"   => $response->json()["recurring_application_charge"]["id"],
                    "price"                             => $response->json()["recurring_application_charge"]["price"],
                    "status"                            => $response->json()["recurring_application_charge"]["status"],
                    "billing_on"                        => $response->json()["recurring_application_charge"]["billing_on"] ?? now(),
                    "test"                              => $response->json()["recurring_application_charge"]["test"],
                    "activated_on"                      => $response->json()["recurring_application_charge"]["activated_on"],
                    "cancelled_on"                      => $response->json()["recurring_application_charge"]["cancelled_on"],
                    "trial_days"                        => $response->json()["recurring_application_charge"]["trial_days"],
                    "capped_amount"                     => $response->json()["recurring_application_charge"]["capped_amount"],
                    "trial_ends_on"                     => $response->json()["recurring_application_charge"]["trial_ends_on"],
                    "balance_used"                      => $response->json()["recurring_application_charge"]["balance_used"],
                    "balance_remaining"                 => $response->json()["recurring_application_charge"]["balance_remaining"],
                    // "confirmation_url"                 => $response->json()["recurring_application_charge"]["confirmation_url"],
                    "plan_handle"                      => $this->data["plan_handle"],
                ]
            );

            $org = DB::table("organizations")->where("id",Auth::user()->organization_id);

            if(!$org->first()->shop_id){
                $org->update(["shop_id" => $this->shopify_channel["shop_id"]]);
            }

            return Redirect::to($response->json()["recurring_application_charge"]["confirmation_url"]);
        }
        else {
            return $error_func(["main" => "Error while charging a charge."]);
        }
    }

    public function redirect_bill($error_callback, $success_callback) {

        if(DB::table("shopify_subscriptions")
            ->where("recurring_application_charge_id", $this->data["charge_id"])->limit(1)
            ->update([
                "status" => "active",
                "organization_id" => Auth::user()->organization_id,
            ])) {
            return $success_callback();
        }
        else {
            return $error_callback();
        }
    }

    public function on_trial($org_id)
    {
        $shopify_subscription = ShopifySubscription::where('organization_id',$org_id)->where('status','active')->latest('id')->first();
        if ($shopify_subscription) {
            return 'trial_started';
        } else {
            return 'no_trial';
        }
    }

    public function is_billed() {
        // TODO: logic for is bill paid
        return false;
    }


    /**
     * Pass shop url and optional argument should_not_billed so user is not billed via shopify.
     *
     *
     * @param  array
     * @return bool
     */
    public function install($error_func)
    {
        $validation = $this->validation($this->install_rules());
        if($validation->fails()) {
            return $error_func($validation->errors());
        }
        else {
            header("Location: " . "https://" . $this->sanitize_shop_url($this->data["shop"]) . "/admin/oauth/authorize?client_id=" . $this->shopify_api_key . "&scope=" . $this->scopes . "&redirect_uri=" . urlencode($this->redirect_uri) . "&grant_options[]=".$this->grant_options.(isset($this->data["should_not_billed"]) ? "&should_not_billed=true" : null));
            die();
        }
    }

    private function register_webhook($topic, $address){

        $response = Http::retry(1, 50)->post($this->shopify_base_url."webhooks.json", [
            "webhook" => [
                "topic" => $topic,
                "address" => env($address),
                "format" => "json"
            ]
        ]);
        if($response->successful()) {
            $json =  $response->json();
            Log::channel('webhook')->info([$json]);
        }
        if($response->failed()) {
            $json = $response->json();
            Log::info("webhook $topic error");
            Log::error([$json]);
        }
    }

    public function redirect($success_func, $error_func) {
        $validation = $this->validation($this->redirect_rules());

        if($validation->fails()) {
            return $error_func($validation->errors());
        }

        $this->data["access_token"] = $this->get_access_token();

        if(!$this->data["access_token"]) {
            return $error_func(["main" => "Their might be some error. Please try again."]);
        }
        $this->data["shop_url"] = $this->data["shop"];

        $shop_data = $this->get_shop_data();
        $shop_data["access_token"] = $this->data["access_token"];
        $shop_data["shop"] = $this->data["shop"];

        //  dd($shop_data);
        /** If you install shopify app from shopify store.
         * first it will check if user is logged in or not
         * if user is logged in it will connect shopify to that user
         * if user is not logged in then it will check if email exists,
         * if email exist then it will log in that user and connect
         * shopify with that account, if email does not exist then
         * it will create a user and then connect shopify with that
         * user.
         * */
        if(!Auth::check()) {
            // $user = User::where("shopify_shop_id", $shop_data["id"])->get()->first();
            // $organization = Organization::where("shop_id", $shop_data["id"])->first();
            // ->orWhere("shop_id",'!=', null)
            // if($organization) {
            //     $user = User::whereHas("organizations", function($query) use($organization){
            //         $query->where(["organization_id" => $organization->id]);
            //     })->first();
            //      $organization->first()->set_active_organization_by_id($organization->id);
            // }else{
                $user = User::where("email", $shop_data["email"])->first();
                // $org = $user?->organizations->first();
                // if($org){
                //     $org->set_active_organization_by_id($org->id);
                // }
                if(!$user) {
                    return redirect(route('shopify.register',['shop' => $shop_data]));
                    // $user = new User();
                    // $user->shopify_shop_id = $shop_data["id"];
                    // $user->fname = $shop_data["shop_owner"];
                    // $user->lname = "";
                    // $user->email = $shop_data["email"];
                    // $user->email_verified_at = Carbon::now();
                    // if(isset($shop_data["phone"]))
                    //     $user->phone = substr($shop_data["phone"],0,15);
                    // $user->save();
                    // $this->createOrganization($user,$shop_data);
                }
                auth()->login($user, true);
            }else{
                $user = Auth::user();
                if(!$user->organization_id){
                    $this->createOrganization($user,$shop_data);
                    // $org = $user?->organizations->first();
                    // if($org){
                    //     $org->set_active_organization_by_id($org->id);
                    // }
                }else{
                    // $this->createOrganization($user,$shop_data);
                }
            }



        // }

        $shopify_channel = $this->where("shop_id", $shop_data["id"])->first();

        if(!$shopify_channel) {
            $channel = new Channel();
            if(Session::has('channel_id')){
                $channel = $channel->findOrFail(Session::get('channel_id'));
            }else{
                $channel = $channel->doesntHave("shopify_channels")->get()->first();
            }
            if(!$channel) {
                if($user->organization_id){
                    if (Gate::denies('create-channel', \App\Models\Channel\Channel::query())) {
                        return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to create new store"]);
                    }
                }
                $channel = new Channel();
                if(!$user->organization_id){
                    $this->createOrganization($user,$shop_data);
                    // if($user->organizations->first()){
                    //     $org = $user->organizations->first();
                    //     $user->organizations->first()->set_active_organization_by_id($org->id);
                    // }else{
                    //     $this->createOrganization($user,$shop_data);
                    // }
                }
                $channel->organization_id = $user->organization_id;
                $channel->name = $shop_data["name"];
                $channel->type = "shopify";
                $channel->save();
            }

            $this->channel_id = $channel->id;
            $this->access_token = $this->data["access_token"];
            $this->shop = $this->data["shop"];
            $this->shop_id = $shop_data["id"];
            $this->save();

            // webhook registration for shopify

            $this->set_base_url($channel->id);
            $this->register_webhook("app/uninstalled","SHOPIFY_WEBHOOK_UNINSTALL_APP" );
            // $this->register_webhook("products/create","SHOPIFY_WEBHOOK_PRODUCT_CREATE" );
            // $this->register_webhook("products/delete","SHOPIFY_WEBHOOK_PRODUCT_DELETE" );
            // $this->register_webhook("collections/create","SHOPIFY_WEBHOOK_CREATE_COLLECTION" );
            // $this->register_webhook("collections/update","SHOPIFY_WEBHOOK_UPDATE_COLLECTION" );
            // $this->register_webhook("products/update","SHOPIFY_WEBHOOK_PRODUCT_UPDATE" );
            // $this->register_webhook("locations/create","SHOPIFY_WEBHOOK_CREATE_LOCATION" );
            // $this->register_webhook("locations/update","SHOPIFY_WEBHOOK_UPDATE_LOCATION" );
            // $this->register_webhook("locations/delete","SHOPIFY_WEBHOOK_DELETE_LOCATION" );
            // // $this->register_webhook("inventory_items/create","SHOPIFY_WEBHOOK_CREATE_INVENTORY" );
            // // $this->register_webhook("inventory_items/update","SHOPIFY_WEBHOOK_UPDATE_INVENTORY" );
            // // $this->register_webhook("inventory_items/delete","SHOPIFY_WEBHOOK_DELETE_INVENTORY" );
            // $this->register_webhook("inventory_levels/update","SHOPIFY_WEBHOOK_INVENTORY_LEVELS" );
            // $this->register_webhook("collections/delete","SHOPIFY_WEBHOOK_DELETE_COLLECTION" );

        }
        else {

            $channel= $shopify_channel->channel()->first();

            if(isset($channel) && $channel->organization_id == Auth::user()->organization_id){
                return redirect(route("dashboard"));
            }else{
                return $error_func(['main' => 'This shop is already connected with another account. One shop can only be connected with one account.']);
            }

        }

        $this->find_channel_by_shopify_id($shop_data["id"]);


        return $success_func($this);
    }

    public function createOrganization($user,$shop_data){

        //set data for saving organization
        preg_match('/\{\{(.*?)\}\}/', $shop_data["money_in_emails_format"], $matches);
        $separator = $matches[1];
        if($separator == "amount_with_comma_separator"){
            $separator = ",";
        }else{
            $separator = ".";
        }

        $org_data = [
            'name'=> $shop_data["name"],
            'region'=> $shop_data["country_code"] ?? 'AM',
            'units'=> $shop_data["weight_unit"] ?? 'SI',
            'currency'=> $shop_data["currency"] ?? 'USD',
            'separator'=> $separator ?? '.',
            'trail_user_days'=> 14,
            'shop_id'=> $shop_data["id"],
        ];


        //save organization
         (new Organization())->set_data($org_data)
            ->set_user($user)
            ->store(
                function($error){
                    Log::info('shopify creating organization error');
                    Log::error($error);
                },function($obj){
                    return  $obj;
                }
            );
    }

    public function getPreviousUsersShopifyLocation($success_func, $error_func)
    {
        $validation = $this->validation($this->redirect_rules());

        if($validation->fails()) {
            return $error_func($validation->errors());
        }

        $this->data["access_token"] = $this->get_access_token();

        if(!$this->data["access_token"]) {
            return $error_func(["main" => "Their might be some error. Please try again."]);
        }
        $this->data["shop_url"] = $this->data["shop"];

        $shop_data = $this->get_shop_data();

        $shopify_channel = $this->where("shop_id", $shop_data["id"])->first();

        if($shopify_channel) {
            $channel = new Channel();
            if(Session::has('update_channel_id')){
                $channel = $channel->findOrFail(Session::get('update_channel_id'));
                $channel->touch();
            }
            $shopify_channel->access_token = $this->data["access_token"];
            $shopify_channel->save();
            $shopify_channel->touch();
        InventoryJob::dispatch(["channel_id" => $channel->id , "user" => auth()->user() , "organization_id" => $channel->organization_id]);
         return $success_func("Shopify updated successfully.");
        }
    }


    public function set_base_url($channel_id){
        $shopify_channel=ShopifyChannel::where('channel_id',$channel_id)->first();
        $this->shopify_base_url         = "https://".$this->shopify_api_key.":".$shopify_channel->access_token."@".$shopify_channel->shop."/admin/api/".self::$API_VERSION."/";
        return $this->shopify_base_url;

    }

    public function get_shop_data() {
        $this->shopify_base_url         = "https://".$this->shopify_api_key.":".$this->data["access_token"]."@".$this->data["shop"]."/admin/api/".self::$API_VERSION."/";

        $response = Http::get($this->shopify_base_url."shop.json");

        if($response->successful()) {
            return $response->json()["shop"];
        }
    }

    /**
     * @deprecated not use in any function
     * */
    public function set_shop_url($shop_url) {
        $this->shop_url                 = $this->sanitize_shop_url($shop_url);
        return $this;
    }

    /**
     * Sanitize shopify url for proper format like "shop-name.myshopify.com".
     *
     * @return string
     */
    public function sanitize_shop_url($shop_url) {
        if (strpos($shop_url, 'http') !== false) {
            $pre_and_post_array = parse_url($shop_url);
            $url= $pre_and_post_array;
        }
        else
        {
            $shop_url = 'http://'.$shop_url;
            $pre_and_post_array = parse_url($shop_url);
            $url= $pre_and_post_array;
        }
        if($url['host'])
            $shop_url = preg_replace('#^www\.(.+\.)#i', '$1', $url['host']);

        return $shop_url;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function bill_rules()
    {
        return [
            'shopify_channel_id'       => ["required"],
            'plan_handle'                  => ['required'],
        ];
    }

    public function sync_rules()
    {
        return [
            'product_id'       => ['required', new ShopifyProductSync($this->data['channel_id'])],
        ];
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function install_rules()
    {
        return [
            'shop'                  => ['required'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function redirect_rules()
    {
        // we want to pass the whole data array to our custom validation rule
        // is_hmac_verified to that is why passing in data associative array.

        return [
            'code'                      => ['required'],
            'hmac'                      => ['required', new is_hmac_verified($this->data, $this->shopify_shared_secret)],
            'host'                      => ['required'],
            'shop'                      => ['required'],
            'timestamp'                 => ['required']
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    public function validation(array $rules) {
        $validator =  Validator::make($this->data, $rules);

        return $validator;
    }


    public function disconnect($error_func , $success_func){

        if (isset($this->data['user_id'])) {
            $user = User::find($this->data['user_id']);
            $user->shopify_shop_id = null;
            $user->save();
        }

        if(isset($this->data['channel_id'])) {

            $channel_id = $this->data['channel_id'];
            $shopify_channel = $this->where('channel_id', $this->data['channel_id'])->first();


            if($shopify_channel) {
                $this->access_token  = $shopify_channel->access_token;
                $this->shop          = $shopify_channel->shop;
                if($this->disconnectShopify() != 200){
                    return $error_func('Something wend wrong. Please try again later.');
                }

                $shopify_channel->delete();

            }
            else {
                return $error_func('Shopify Account Not found.');
            }
        }
        else if(isset($this->data['shop_name'])){
            $shopify_channel = $this->where('shop', $this->data['shop_name'])->first();
            if($shopify_channel) {
                $channel_id = $shopify_channel->channel_id ;
                $shopify_channel->delete();
            }
            else {
                return $error_func('Shopify Account Not found.');
            }
        }
        if($channel_id){
            $channel_product_ids = ChannelProduct::where('channel_id',$channel_id)->get()->pluck('id')->toArray();
            ChannelProductStatus::whereIn('channel_product_id',$channel_product_ids)
                ->where(['type'=>'shopify'])
                ->delete();

            (new ShopifySubscription())->removeShopifyBilling($channel_id);

            return $success_func();
        }
    }

    /**
     * @param $product_id
     * @param $channel_id
     * @return array
     */
    public function get_product_channel_version_ids(int $product_id = null, int $channel_id = null): array
    {
        $product_id = $product_id??$this->data['product_id'];
        $channel_id = $channel_id??$this->data['channel_id'];
        $product_version_id = ProductVersion::where('product_id',$product_id)->pluck('version_id')->toArray();

        return ChannelVersion::query()->where('channel_id',$channel_id)->whereIn('version_id',$product_version_id)->pluck('version_id')->toArray();
    }
    /**
     * create the product attribute array
     * @deprecated not use in any function
     */
    private function get_single_values($array=null)
    {
        $name = null;
        foreach($array as $value)
        {
            $name= $value->name;
            break;
        }
        return $name;
    }



    /**
     * convert the apimio product array to shopify array
     * @param $id
     * @param $version_id
     * @param $product_obj
     * @return array
     */
    public function set_array_product($id = null, $version_id, $product_obj = null)
    {
        if ($product_obj == null){
            $products = new Product();
            $this->product = $products->with(["brands",
                "categories",
                "vendors",
                "channels",
                "variants",
                "files",
                'versions'=>function($q) use ($version_id){
                    $q->where('version_id',$version_id);
                }])
                ->findOrFail($id);
            $this->product = $products->product_fetch($this->product);
        }
        else{
            $this->product = $product_obj;
        }

        // get options array
        $count1=1;
        $options = array();
        if(sizeof($this->product->variants) > 0)
        {
            foreach( $this->product->variants as $variant)
            {
                $variant_decode = json_decode($variant->option,TRUE);
                if(isset($variant_decode['attributes'])) {
                    foreach($variant_decode['attributes'] as $key=>$attribute)
                    {
                        $options[$key]['name']=$attribute['name'];
                        $options[$key]['position']=$count1;
                        $options[$key]['values']=$attribute['options'];
                        $count1++;
                    }
                }
            }
        }
        else
        {
            $options[0]['name']="Title";
            $options[0]['position']=1;
            $options[0]['values']=["Default Title"];
        }

        //get variants and general, seo fields
        $attribute_variant =  $this->get_attribute_value( $this->product ,$version_id );
        //get images array list
        $count=1;
        $images=array();
        $image=null;
        foreach ( $this->product->files as $file)
        {
            $images[] = [
                "src"=> $file->link,
                "width"=> $file->width,
                "height"=> $file->height,
                "position"=> $count,
                "created_at"=> $file->created_at,
                "updated_at"=> $file->updated_at,
            ];
            if($count == 1)
            {
                $image = $file->link;
            }
            $count++;
        }

        $array= [
            'vendor'=>$this->get_single_values( $this->product['brands']),
            'product_type'=>$this->get_single_values( $this->product['categories']),
            'status'=> $this->product->status?'active':'draft',
            "tags"=>[],
            'created_at'=> $this->product->created_at,
            'updated_at'=> $this->product->updated_at,
            'published_at'=> $this->product->updated_at,
            'options'=>$options,
            'images'=>$images,
            'image'=>$image,
            // 'metafields' =>  (new SyncMetafield())->createAttributeArray($this->product)
        ];
        $array1=array_merge($attribute_variant,$array);

        return $array1;

    }



    public function changeQueueStatus($channel_id, $status){
        $shopifyChannel = ShopifyChannel::query()
            ->where('channel_id',$channel_id)
            ->first();
        if($shopifyChannel){
            $shopifyChannel->is_queue_start = $status;
            $shopifyChannel->save();
        }
    }


//=========relationships ================//
    public function channel() {
        return $this->belongsTo(Channel::class)->withoutGlobalScopes();
    }

    public function shopify_subscription() {
        return $this->hasOne(ShopifySubscription::class);
    }
}
