<?php

namespace App\Jobs;

use App\Models\EventStatusHandler;
use App\Notifications\ApimioNotification;
use App\Traits\AWSSetting;
use App\User;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FinalNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, AWSSetting, Batchable;

    /**
     * Create a new job instance.
     * @param array $data
     * @return void
     *  $data = [
     *     'organization_id' => $organization_id, // optional
     *     'user_id' => $user_id,
     *     'details' => $details,
     *     'email_notification' => $email_notification, // optional, true or false, default is false,
     *
     */
    public function __construct(public array $data)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
//        info('FinalNotificationJob: ' . json_encode($this->data));

        $send_to = $this->data['send_to'] ?? false;

        if (! empty($this->data['method'])) {
            // e.g. "App\Classes\ImportExport@get_notification_for_import"
            $methodString = $this->data['method'];

            // Split into class + method
            [$class, $method] = explode('@', $methodString);

            // Resolve the class instance
            $instance = app($class);

            // Grab any params you want to pass to the method
            // e.g. an array of parameters from $this->data['method_params']
            $parameters = $this->data['method_params'] ?? [];

             $this->data = call_user_func_array([$instance, $method], $parameters);
        }



        $notifier = new ApimioNotification($this->data['details']);

        //if email_notification is  to true, then send email notification
        if(!$send_to || $send_to && !in_array('email', $send_to)) {
            $notifier->only_db_notify(true);
        }

        $user = User::query()->find($this->data['user_id']);
        $user->notify($notifier);

        //delete the listener handler table record for this event
        (new EventStatusHandler())
            ->set_data([
            'organization_id' => $this->data['organization_id']
            ])->delete_event();
    }
}
