<?php

namespace App\Http\Requests\Attribute;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Product\Family;
use App\Rules\AttributeValidationRule;
class AttributeRequest extends FormRequest
{
    protected $data; // Custom data attribute

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare data before validation.
     */
    protected function prepareForValidation()
    {
       //
    }

    /**
     * Modify the data after validation.
     *
     * This method is called after validation runs.
     *
     * We add the organization_id to the validated data.
     */
    protected function passedValidation()
    {
        
        if(get_class($this->user()) == 'App\Models\Organization\Organization'){
            $organization_id = $this->user()->id;
        }
        else{
            $organization_id = $this->user()->organization_id;
        }
        
        
        $this->merge([
            'organization_id' => $organization_id, // Assuming the authenticated user is the sender
        ]);
    }


    /**
     * Add custom validation logic after the main rules are applied.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (isset($this->data['attribute_family'])) {
                $this->validateFamilyIds($validator);
            }
        });
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        $rules = [
            'organization_id' => 'prohibited',
            "attribute_type_id" => "required",
            'name' => 'required|unique:attributes,name,' . $this->route('attribute') . ',id,organization_id,' . auth()->user()->id,
            "attribute_options" => "required_if:attribute_type_id,4,13",
            "handle" => "nullable",
            "rules" => "nullable",
            "attribute_options.*.name" => ["required", "max:255"],
            "description" => "max:250",
            "attribute_family" => ["array", Rule::requiredIf(isset($this->data['method_type']) && !in_array($this->data['method_type'], ['import', 'clone']))],
            "attribute_family.*" => "exists:families,id", // Ensure all family IDs exist
        ];

        // Conditional rules for date validation
        if ((isset($this->data['type']) && $this->data['type'] == 'date') &&
            isset($this->data['start_date'], $this->data['end_date'])) {
            $rules['start_date'] = 'before_or_equal:end_date';
        } elseif ((isset($this->data['type']) && $this->data['type'] == 'date_and_time') &&
            isset($this->data['start_date_time'], $this->data['end_date_time'])) {
            $rules['start_date_time'] = 'before_or_equal:end_date_time';
        }

        // Rules if `is_required` is true
        if (isset($this->data['is_required']) && $this->data['is_required'] == 1) {
            $rules['min'] = new AttributeValidationRule($this->data);
        }

        return $rules;
    }

        /**
     * Validate that all provided family IDs exist.
     */
    private function validateFamilyIds($validator)
    {
        $families = $this->data['attribute_family'];
        $existingFamilies = Family::whereIn('id', $families)->pluck('id')->toArray();

        // Find any invalid family IDs
        $invalidFamilies = array_diff($families, $existingFamilies);

        if (!empty($invalidFamilies)) {
            $validator->errors()->add(
                'attribute_family',
                'The following family IDs are invalid: ' . implode(', ', $invalidFamilies)
            );
        }
    }

    /**
     * Get custom error messages for validation.
     */
    public function messages(): array
    {
        return [
            'attribute_type_id.required' => 'The Attribute type is required',
            'name.required' => 'The Attribute name field is required',
            'name.min' => 'The name must be at least 3 characters.',
            'name.max' => 'The name cannot exceed 255 characters.',
            'attribute_options.required_if' => 'Please add attribute options to create a multi-select dropdown.',
            'attribute_options.*.name.required' => 'Attribute Option cannot be empty.',
            'attribute_options.*.name.max' => 'Attribute Option name cannot be greater than 255 characters.',
            'attribute_family.required' => 'Please select an attribute set.',
            'attribute_family.*.exists' => 'One or more of the selected families do not exist.',
            'start_date.before_or_equal' => 'Start date must be before or equal to the end date.',
            'start_date_time.before_or_equal' => 'Start date-time must be before or equal to the end date-time.',
        ];
    }



}
