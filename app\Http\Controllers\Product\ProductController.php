<?php

namespace App\Http\Controllers\Product;

use App\User;
use Illuminate\Http\Request;
use App\Models\BatchProgress;
use App\Models\Product\Family;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Product\Version;
use Dflydev\DotAccessData\Data;
use Apimio\Gallery\Models\Folder;
use App\Models\Location\Location;
use App\Models\Product\Inventory;
use App\Jobs\Bulk\BulkDeleteQueue;
use App\Classes\Facades\UnitFacade;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\View;
use App\Events\Product\ManageVersion;
use Illuminate\Http\RedirectResponse;
use App\Models\Channel\ChannelProduct;
use App\Models\Product\ProductVersion;
use Illuminate\Contracts\View\Factory;
use App\Events\ChannelUpdateStatusEvent;
use Illuminate\Support\Facades\Redirect;
use App\Models\Organization\Organization;
use App\Notifications\ApimioNotification;
use App\Models\Channel\ChannelProductStatus;
use Illuminate\Contracts\Foundation\Application;
use App\Models\Product\AttributeFamilyProductVersion;
use Inertia\Inertia;
class ProductController extends Controller
{

    public function __construct()
    {
        $this->middleware(['auth', 'verified', "activeOrganization"]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function index()
    {
        if (Gate::denies('SubscriptionAccess', 'product')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $org_id = Auth::user()->organization_id;
        $batches_progress = BatchProgress::where('user_id', Auth::user()->id)
            ->where('organization_id', Auth::user()->organization_id)
            ->where('status', 0)
            ->get();
        // $products = Product::where('organization_id', $org_id)->get();
           return view('products.index', compact('org_id', 'batches_progress'));
        // return Inertia::render('ProductListing', [
        //     'org_id' => $org_id,
        //     'batches_progress' => $batches_progress,
        //     'products' => $products,
        // ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $response = Gate::inspect('create', Product::class);
        // if ($response->allowed()) {
            $product = new Product();
            $data = $request->all();
            $data = $data+ ['organization_id'=>auth()->user()->organization_id];
            return $product->set_data($data)->store(
                // when error
                function ($errors) {
                    return back()->withInput()->withErrors($errors);
                },
                // when success
                function ($obj) {
                    return redirect()->route("products.edit", $obj->id);
                }
            );
        // } else {
        //     return \redirect(route('dashboard'))->withErrors(['main' => $response->message()]);
        // }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  $id
     * @param null $version_id
     * @return Application|Factory|RedirectResponse|\Illuminate\View\View
     */
    public function edit($id, $version_id = null)
    {
        // $this->authorize('add_and_edit_product', [\App\Models\Organization\OrganizationUserPermission::class, auth()->user()->organization_id]);
        if (Gate::denies('SubscriptionAccess', 'product')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $product_version_id = null;

        $products = new Product();
        $product = $products->fetch(function ($product) use ($id, $version_id) {
            return $product->findOrFail($id);
        });

        $product_versions = $product->versions;

        $versions = Version::all();

        if ($version_id) {

            $productVersion = $product->versions->where("id", $version_id)->first();
            if(!$productVersion){
                event(new ManageVersion(
                    $product,
                    (isset($version_id) ? array( ['id' => $version_id]) : [])
                ));
                $productVersion = $product->versions->where("id", $version_id)->first();
            }

            $current_version = Version::findOrFail($version_id);

            $score =  $current_version->getScoreByProductId($id);
        } else {
            if ($product->versions->count() > 0) {
                $version = $product->versions->first();
            } else {
                $version = $versions->first();
                if (!$version)
                    return Redirect::route("version.add")->withErrors(["main" => "Please create at least one version to create your product."]);
            }
            return Redirect::route("products.edit", ["id" => $id, "version_id" => $version->id]);
        }

        if ($productVersion) {
            $product_version_id = ProductVersion::where("product_id", $id)->where("version_id", $productVersion->id)->pluck("id")->first();
            $default_families = $productVersion->families->where("is_default", "=", 1);
            $family_name = $default_families->pluck('name')->toArray();
            if (!in_array('General', $family_name)) {
                $default_families = new Family();
                $default_families = $default_families->fetch_data(function ($family) {
                    return $family->where('is_default', 1)->get();
                });
            }
        } else {
            $default_families = new Family();
            $default_families = $default_families->fetch_data(function ($family) {
                return $family->where('is_default', 1)->get();
            });
        }
        //getting count of filled or empty fields of product
        $fields_score = $product->product_fields_score($id);

        $families = new Family();
        $families = $families->fetch_data(function ($family) {
            return $family->where('is_default', 0)->get();
        });

        $organization_settings = Organization::findOrFail(Auth::user()->organization_id);

        $currency = Version::findOrFail($version_id);

        $units = UnitFacade::get_units();

        $locations = Location::all();
        //  to fetch variants
        $variants = Variant::where('product_id', $id)->where('version_id', $version_id)->get();
        return view('products.add', compact(
            'product',
            'variants',
            'families',
            'default_families',
            'versions',
            "product_version_id",
            "version_id",
            "product_versions",
            "current_version",
            "score",
            'fields_score',
            'organization_settings',
            'currency',
            'locations',
            'units'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  $id
     * @param null $version_id
     * @return Application|Factory|RedirectResponse|\Illuminate\View\View
     */
    public function seo_edit($id, $version_id = null)
    {
        $this->authorize('add_and_edit_product', [\App\Models\Organization\OrganizationUserPermission::class, auth()->user()->organization_id]);

        $products = new Product();
        $product = $products->fetch(function ($product) use ($id, $version_id) {
            return $product->findOrFail($id);
        });

        $product_versions = $product->versions;
        $versions = Version::all();

        if ($version_id) {
            $product->versions = $product->versions->where("id", $version_id)->first();
            $current_version = Version::findOrFail($version_id);

            $score =  $current_version->getScoreByProductId($id);
        } else {
            if ($product->versions->count() > 0) {
                $version = $product->versions->first();
            } else {
                $version = $versions->first();
                if (!$version)
                    return Redirect::route("version.add")->withErrors(["main" => "Please create at least one version to create your product."]);
            }
            return Redirect::route("products.edit", ["id" => $id, "version_id" => $version->id]);
        }

        if ($product->versions) {
            $default_families = $product->versions->families->where("is_default", "=", 1)->where('name', 'SEO');
            $family_name = $default_families->pluck('name')->toArray();
            if (!in_array('SEO', $family_name)) {
                $default_families = new Family();
                $default_families = $default_families->fetch_data(function ($family) {
                    return $family->where('is_default', 1)->where('name', 'SEO')->get();
                });
            }
        } else {
            $default_families = new Family();
            $default_families = $default_families->fetch_data(function ($family) {
                return $family->where('is_default', 1)->where('name', 'SEO')->get();
            });
        }

        return view('products.seo_fields', compact(
            'product',
            'default_families',
            'versions',
            "version_id",
            "product_versions",
            "current_version",
            "score"
        ));
    }

    public function pre_product_edit_data($id, $version_id)
    {
        $products = new Product();
        $product = $products->fetch(function ($product) use ($id) {
            return $product->findOrFail($id);
        });

        $version = Version::findOrFail($version_id);

        return [
            "product" => $product,
            "version" => $version
        ];
    }

    public function attribute_set(Request $request, $id, $version_id)
    {

        $data = $this->pre_product_edit_data($id, $version_id);

        $product = $data["product"];
        $version = $data["version"];

        $product_families = array();
        $family_ids = [];

        if ($request->filled("family_ids")) {
            $requested_family_ids = explode(",", $request->get("family_ids"));
            foreach ($requested_family_ids as $family_id) {
                if ($product->versions->find($version->id) ? $product->versions->find($version->id)->families->where("id", $family_id)->count() == 0 : true) {
                    $product_families[] = Family::find($family_id);
                    $family_ids[] = $family_id;
                }
            }
        }

        $current_version = $product->versions->find($version->id);
        if ($current_version) {
            foreach ($current_version->families->where("is_default", 0) as $family) {
                $product_families[] = $family;
                $family_ids[] = $family->id;
            }
        } else {
            $current_version = Version::findOrFail($version_id);
        }

        $families = Family::where("is_default", 0)->get();
        $product_versions = $product->versions;
        $versions = Version::all();

        return \view("products.manage_attribute_set", compact(
            "product",
            "version",
            "families",
            "product_families",
            "family_ids",
            'current_version',
            'versions',
            'product_versions'
        ));
    }

    public function media($id, $version_id = null)
    {
        $data = $this->pre_product_edit_data($id, $version_id);

        $product = $data["product"];
        $version = $data["version"];
        $image_quality = $data["product"]->imageQualityScoreByProductId();
        $folders = Folder::where('type', 'social_media')->get();

        return \view("products.manage_media", compact("product", "version", "image_quality", "folders"));
    }

    public function score($id, $version_id = null)
    {
        $data = $this->pre_product_edit_data($id, $version_id);

        $product = $data["product"];
        $version = $data["version"];

        return \view("products.manage_score", compact("product", "version"));
    }

    public function catalog($id, $version_id = null)
    {
        $data = $this->pre_product_edit_data($id, $version_id);

        $product = $data["product"];
        $version = $data["version"];

        return view("products.catalog", compact("product", "version"));
    }
    /** Show the Quantity of variant locations_wise/Store_wise for editing the specified resource.
     * @param $id
     * @param $version_id
     * @return Application|Factory|\Illuminate\Contracts\View\View
     */
    public function inventory($id, $version_id = null)
    {
        if (Gate::denies('SubscriptionAccess', 'inventory')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $data = $this->pre_product_edit_data($id, $version_id);
        $product = $data["product"];
        $version = $data["version"];

        $channels = Channel::with(["channelWiseLocation" => function ($query) use ($id) {
            $query->with(['location',"inventory" => function ($query)  use ($id) {
                $query->whereIn("variant_id", Variant::where("product_id", $id)->pluck("id")->toArray());
                $query->where(["organization_id" => auth()->user()->organization_id]);
            }]);
        },
        "products"
        => function ($query) use ($id,$version) {
        $query->with(["inventories", "variants" => function ($query) use ($version) {
            $query->with("inventories", "file")->where("version_id", $version->id);
        }])
        ->where("products.id", $id);
        }
        ])->whereHas("products", function ($query) use ($id) {
            $query->where("products.id", $id);
        })->get();

        return view("products.manage_inventory", compact("product", "version", "channels"));
    }

    public function newUpdateInventory(Request $request)
    {
        try {
            $product = Product::find($request->product_id);
            if($product){
                $request->validate([
                    'available_quantity.*.*' => 'nullable|integer|min:0|max:999999999'
                ]);
                Inventory::updateOrCreate(
                    [
                        'product_id' => $request->product_id,
                        'organization_id' => $product->organization_id,
                        'location_id' => $request->location_id,
                        'variant_id' => $request->variant_id
                    ],
                    ['available_quantity' => ($request->quantity !== null ? $request->quantity : 0)]
                );
            }else{
                $variant = Variant::findOrFail($request->variant_id);
                if($variant){
                    $request->validate([
                        'track_quantity.*.*' => 'required',
                        'continue_selling.*.*' => 'required',
                    ]);
                    $array = [];
                    $array["track_quantity"] = $request->track_quantity;
                    $array["continue_selling"] = $request->continue_selling;
                    }

                    if ($variant->settings()->first() !== null) {
                        $variant->settings()->update($array);
                    } else {
                        $variant->settings()->create($array);
                    }
                }
                if (!$product) {
                    if ($variant) {
                        $product = Product::find($variant->product_id);
                    }
                }
                if ($product) {
                    event(new ChannelUpdateStatusEvent(
                        product: $product,
                        isUpdated: true
                    ));
                }


            return ['status' => true , 'message' => 'success'];
        } catch (\Exception $e) {
            return ['status' => false , 'message' => $e] ;
        }


    }

    /**UpdateOrCreate the Quantity of variant for editing the specified resource.
     * @param Request $request
     * @param $id
     * @param $version_id
     * @return RedirectResponse
     */
    public function updateInventory(Request $request, Product $product, $version_id = null)
    {
        // 1. Validate the request data
        $request->validate([
            'quantities.*.*' => 'nullable|integer|min:0|max:999999999',
            'product_quantities.*' => 'nullable|integer|min:0|max:999999999'
        ]);

        // 2. Update variant quantities
        if ($request->has('quantities')) {
            foreach ($request->quantities as $location_id => $variants) {
                $is_variant_update = true;
                foreach ($variants as $variant_id => $quantity) {
                    // Ensure the variant_id is an integer
                    $variant_id = (int) $variant_id; // Casting to integer
                    if ($is_variant_update) {
                        $variant = Variant::findOrFail($variant_id);
                        if (isset($variant)) {
                            $array = array();
                            if (isset($request->track_quantity[$variant->id])) {
                                $array["track_quantity"] = 1;
                            } else {
                                $array["track_quantity"] = 0;
                            }
                            if (isset($request->continue_selling[$variant->id])) {
                                $array["continue_selling"] = 1;
                            } else {
                                $array["continue_selling"] = 0;
                            }

                            if ($variant->settings()->first() !== null) {
                                $variant->settings()->update($array);
                            } else {
                                $variant->settings()->create($array);
                            }
                        }
                    }

                    // Update or create inventory record
                    // if($quantity) {
                    // Inventory::updateOrCreate(
                    //     [
                    //         'product_id' => $product->id,
                    //         'organization_id' => $product->organization_id,
                    //         'location_id' => $location_id,
                    //         'variant_id' => $variant_id
                    //     ],

                    //     ['available_quantity' => ($quantity !== null ? $quantity : 0)]
                    // );
                    // }
                }
                $is_variant_update = false;
            }
        }

        // 3. Update product quantities if there are no variants
        if ($request->has('product_quantities')) {

            if (isset($product)) {
                $array = array();
                if (isset($request->track_quantity[$product->id])) {
                    $array["track_quantity"] = 1;
                } else {
                    $array["track_quantity"] = 0;
                }
                if (isset($request->continue_selling[$product->id])) {
                    $array["continue_selling"] = 1;
                } else {
                    $array["continue_selling"] = 0;
                }

                if ($product->settings()->first() !== null) {
                    $product->settings()->update($array);
                } else {
                    $product->settings()->create($array);
                }
            }

            foreach ($request->product_quantities as $location_id => $quantity) {
                // If there's no variant, variant_id should be null or 0 based on your DB design

                $variant_id = 0; // or null if your database design allows null values for variant_id
                // if($quantity) {
                Inventory::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'location_id' => $location_id,
                        'organization_id' => $product->organization_id,
                        'variant_id' => null
                    ],

                    ['available_quantity' => ($quantity !== null ? $quantity : 0)]
                );
                // }
            }
        }


        // 4. Redirect back with success message
        return redirect()->route('products.inventory', ["product" => $product->id, 'version_id' => $version_id])->with('success', 'Inventory updated successfully.');
    }

    /**
     * @deprecated Use event(new ChannelUpdateStatusEvent) instead
     */
    public function changeSyncChannelStatus($obj,$channels){
        if(isset($channels) && count($channels) > 0){
            foreach($channels as $key => $channel_id){
                $channel_product = ChannelProduct::where([
                    'channel_id'=>$channel_id,
                    'product_id'=>$obj->id
                ])->first();
               if($channel_product){
                    $product_status = ChannelProductStatus::firstOrCreate(
                        [
                            'channel_product_id' => $channel_product->id,
                            'organization_id' => $obj->organization_id,
                            'type' => 'shopify'
                        ],
                        [
                            'created_at' => now(),
                            'updated_at' => now(),
                            'status' => 0,
                        ]
                    );

                    if($product_status){
                        $product_status->status = 0;
                        $product_status->save();
                    }
                }

            }
        }
    }
    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $product = new Product();
        return $product->set_data($request->all())
            ->set_id($id)
            ->store(
                // when error
                function ($errors) {
                    return back()->withInput()->withErrors($errors);
                },
                // when success
                function ($obj) use ($request, $id) {
                    if ($request->has('variant_submit')) {
                        return redirect()->route('variants.step.three', ['id' => $id]);
                    } else {
                        return back()->withSuccess("Product updated successfully")->with(['sync_popup' => true]);
                    }
                }
            );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
    // from Mubashir use this code for single delete.
        $product = Product::query()->find($id);
        if($product){
            $product->delete();
            return redirect()->route('products.index')->withSuccess("Product delete successfully");
        }

         return redirect()->route('products.index')->with(['error'=>"Product not found."]);
    }

    public function destroyJson($id)
    {
    // from Mubashir use this code for single delete.
        $product = Product::query()->find($id);
        if($product){
            $product->delete();
            return response()->json(['success'=>"Product delete successfully"],200);
        }

          return response()->json(['error'=>"Product not found."],404);
    }

    /**
    *@deprecated This functions is not being used anywhere in the code
     */
    public function familyAttributes(Request $request)
    {
        $ids = $request->get('id');
        $product_version_id = $request->get('product_version_id');

        $family = new Family();
        $families = $family->fetch_data(function ($family) use ($ids) {
            return $family->whereIn('id', $ids)->get();
        });

        foreach ($families as $family) {
            foreach ($family->attributes as $attribute) {
                $attribute->value = AttributeFamilyProductVersion::where("attribute_family_id", $attribute->pivotId)
                    ->where("product_version_id", $product_version_id)
                    ->pluck("value")->first();
            }
        }
        return $families;
    }

    public function bulk_select_all_products(Request $request)
    {

        $products = Product::get()->pluck('id')->toArray();
        return $products;
    }

    public function bulk_delete_all_products(Request $request)
    {
        if($request->ids == "all"){
            $products = Product::get()->pluck('id')->toArray();
            $ids = $products;
        }
        else{
            $ids = explode(",", $request->ids);
        }


        $data['required_data']['organization_id'] = (string)auth()->user()->organization_id;
        $data['required_data']['user'] = auth()->user();
        $batchSize = 10; // Set the batch size as needed.
        $batches_chunks  = array_chunk($ids, $batchSize);

        try {

            $user = User::findOrFail(auth()->user()->id);
            $batch_bulk_delete = Bus::batch([])
                ->then(function () use ($user, $data) {
                    // on success notification
                    $details = [
                        'subject' => 'Product bulk delete successfully.',
                        'greeting' => 'Thank you for using ' . request()->getHttpHost(),
                        'body' =>  "All products successfully Deleted.",
                        'thanks' => '',
                        'actionText' => 'view',
                        'actionURL' => '',
                        'user_id' => $data['required_data']['user']->id,
                        'organization_id' => $data['required_data']['organization_id'],
                    ];
                    $notifier = new ApimioNotification($details);
                    //un-comment if you want to stop notification in email
                    $notifier->only_db_notify(true);
                    $user->notify($notifier);
                })->dispatch();

            // below code is for batch progress monitoring
            $batch_progress_data = [
                'user_id' => $data['required_data']['user']->id,
                'organization_id' => $data['required_data']['organization_id'],
                'batch_id' => $batch_bulk_delete->id ?? null,
                'type' => 'bulk_delete'
            ];
            (new BatchProgress())->store($batch_progress_data);
            // Chunk the selected product IDs and add them to the batch for deletion.
            foreach ($batches_chunks  as $chunk) {

                $batch_bulk_delete->add(new BulkDeleteQueue($data, $chunk));
            }
            $msg = 'Bulk Products is scheduled for deletion. You will receive a notification once its deleted.';

            return Redirect::route("products.index")->withSuccess($msg);
        } catch (\Exception $e) {
            Log::error(["main" => $e->getMessage()]);
            return Redirect::back()->withErrors("Something went wrong.! Please try again later.");
        }
    }
}
