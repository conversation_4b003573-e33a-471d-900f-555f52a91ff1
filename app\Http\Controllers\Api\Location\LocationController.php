<?php

namespace App\Http\Controllers\Api\Location;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use App\Models\Location\Location;

class LocationController extends Controller
{


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {

            $locations = $request->user()->locations();
            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            $paginatedLocations = $locations->orderBy('created_at', 'desc')->paginate($paginate);

            // Return the response with pagination details
              // Prepare pagination details
              $pagination = [
                'current_page' => $paginatedLocations->currentPage(),
                'last_page' => $paginatedLocations->lastPage(),
                'per_page' => $paginatedLocations->perPage(),
                'total' => $paginatedLocations->total(),
            ];

            // Return the response with pagination details
            return response(
                [
                    'message' => 'Attributes retrieved successfully',
                    'locations' => $paginatedLocations->items(),
                    'pagination' => $pagination
                ]
            );
        } catch (\Exception $e) {
            return response()->json(['message' => 'An error occurred while retrieving locations', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
