<?php

namespace App\Listeners;

use App\Traits\AWSSetting;
use App\User;
use Illuminate\Support\Facades\Storage;
use Throwable;
use Illuminate\Bus\Batch;
use App\Events\PostBillingEvent;
use App\Jobs\Shopify\LocationJob;
use App\Models\EventStatusHandler;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Jobs\Shopify\FetchMetaFields;
use App\Jobs\Shopify\FetchCollections;
use App\Models\Channel\ShopifyChannel;
use App\Jobs\Shopify\UpdateInventoryJob;
use App\Notifications\ApimioNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Jobs\Shopify\FetchMetaFieldDefinitions;
use Ghazniali95\ShopifyConnector\App\Classes\Services\ProductService;

class FetchShopifyProduct implements ShouldQueue
{

    use AWSSetting;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
      $this->initializeTrait();
    }

    /**
     * @param $event
     * @return bool
     */
    private function getEventStatus($event): bool
    {
        $e = new EventStatusHandler();
        $e = $e->set_data([
            'organization_id' => $event->organization_id,
            'listener_name' => 'FetchShopifyProduct'
        ])->get_event();
        if ($e) {
            return true;
        } else {
            return false;
        }
    }


    public function handle($event): void
    {
        $res = $this->getEventStatus($event);

        if ($res) {
            $channel = ShopifyChannel::query()->where('channel_id', $event->channel_id)->first();
            if ($channel) {
                //start batching
                $batch = Bus::batch([])
                    ->then(function (Batch $batch) use ($event) {
                        // All jobs completed successfully
                        $path = route('products.index');
                        $details = [
                            'subject' => 'Shopify Products import to Apimio successfully.',
                            'greeting' => 'Hi ',
                            'body' => "Your import Shopify products queue is processed successfully. Please upgrade plan to add more products.",
                            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
                            'actionText' => 'View',
                            'actionURL' => url($path),
                            'user_id' => $event->user_id,
                            'organization_id' => $event->organization_id,
                        ];

                        $notifier = new ApimioNotification($details);

                        //un-comment if you want to stop notification in email
                        $notifier->only_db_notify(true);
                        $user  = User::query()->find($event->user_id);
                        $user->notify($notifier);
                        $e = new EventStatusHandler();
                        $e->set_data(['organization_id' => $event->organization_id])->delete_event();
                    })->dispatch();
                  $count = 0;

                //FETCH and save collection values
                $batch->add(new FetchCollections($event));
                //FETCH and save metafield definitions
//                $batch->add(new FetchMetaFieldDefinitions($event));
//                //FETCH and save Location
//                $batch->add(new LocationJob($event));

                  //Fetch product from shopify using gql
                    $shopifyService = new \App\Classes\Shopify\FetchDataByGQL($event->channel_id);
                    $shopifyService->fetchProducts(  function ($products) use ($event, $batch, &$count) {
                        // Chunk the products into groups of 5
                        $productChunks = array_chunk($products, 1);
                        foreach ($productChunks as $chunk) {
                            // Calculate how many products can be processed in this chunk
                            $processableProducts = [];
                            foreach ($chunk as $product) {
                                $processableProducts[] = $product;
                            }
                            info('count starts,'. $count++);
                            if (!empty($processableProducts)) {
                                if(env('CUSTOM_QUEUE_WORKER') == 'local'){
                                    $s3Url =  $processableProducts;
                                }else{
                                    $s3Url = $this->saveToS3($processableProducts, 'shopify-products-' . $count);
                                }
                                $batch->add(new \App\Jobs\Shopify\FetchShopifyProduct( $s3Url, $event ));
                            }
                        }
                    });





//                //FETCH and save metafields values
//                $batch->add(new FetchMetaFields($event));
//
//                // Update Inventory Location
//                $batch->add(new UpdateInventoryJob($event));
            }

            //TODO: AFTER TESTING THE NEW BATCHING FLOW DELETE THIS BELOW CODE
            //            if($channel) {
            //                $channel->getProducts(function ($payload) use ($event) {
            //
            //                    $shopifyConvertPayload = new ConvertPayload();
            //                    $shopifyConvertPayload->shopifyProductToApimio($payload, function ($product) use ($event) {
            //
            //                        $products = new ConvertToApimioProduct();
            //                        return $products->convert_shopify_to_apimio_product(
            //
            //                        //error response
            //                            function ($error) {
            //                                Log::error('shopify product import error');
            //                                Log::error($error);
            //                            },
            //
            //                            //success response
            //                            function () use ($products) {
            //                                Log::info('shopify product import success');
            //                            },
            //                            $product,
            //                            [
            //                                'save_to'=>$event->organization_id,
            //                                'check_from'=>$event->organization_id
            //                            ]);
            //                    },$event);
            //                });
            //
            //
            //                 //save meta field definitions
            //                        $metafield_definition =  new MetaFieldDefinition($event->channel_id ,$event->organization_id);
            //                        $metafield_definition =  $metafield_definition->getDefinitions()->getAttributeArray();
            //                        $metafield_definition->saveAttributeSet();
            //                        $metafield_definition->store();
            //
            //                // save metafields
            //                       (new Metafield($event->channel_id, $event->organization_id))->getProductsId()->getMetaFields();
            //
            //                //save collections and link it with product
            //                $retrieve_collections = new RetrieveCollection($event->channel_id, $event->organization_id);
            //                $retrieve_collections->getCollection();
            //                $retrieve_collections->getCollectProduct();
            //
            //            }
        }
    }

    /**
     * Determine whether the listener should be queued.
     *
     * @param  PostBillingEvent  $event
     * @return bool
     */
    public function shouldQueue($event): bool
    {
        $res = $this->getEventStatus($event);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }

    function saveToS3($data, $fileName) {
        $path = 'queue-payload/' . $fileName . '.json';
        Storage::disk('s3')->put($path, json_encode($data));
        return $path;
    }

}
