<?php

namespace App\Jobs\Shopify\Webhooks;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Channel\ShopifyChannel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class UninstallAppJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $headers;

    /**
     * Create a new job instance.
     */
    public function __construct( $headers )
    {
        $this->headers = $headers;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        DB::beginTransaction();
        try {
            $channel = new ShopifyChannel();
            $channel = $channel->set_data(['shop_name' => $this->headers['X-Shopify-Shop-Domain']])
                ->disconnect(function ($error) {
                    Log::channel('webhook')->debug($error);
                }, function () {
                    Log::channel('webhook')->info("account delete successful");
                });
            DB::commit();
            // return $channel;
        } catch (\Exception $e) {
            DB::rollback();
            Log::channel('webhook')->debug($e->getMessage());
        }
    }
}
