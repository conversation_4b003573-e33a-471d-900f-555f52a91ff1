<?php

namespace App\Http\Controllers\Api\Filter;

use App\Http\Controllers\Controller;
use App\Http\Requests\Filter\FilterRequest;
use App\Traits\Api\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FilterController extends Controller
{
    use ApiResponseTrait;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {

            $user = $request->user();

            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }


            $filters_data = $this->getApplyFilterData();

            // Return the response with pagination details
            return $this->successResponse(
                'Filters Data retrieved successfully',
                $filters_data,
                200,
            );

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve filters data', $e->getCode()!= 0 ? $e->getCode() : 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FilterRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FilterRequest $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Get filter data
     */
    public function getApplyFilterData(): array
    {
        $type_hint = [
            'is_defined' => 'Is Defined',
            'is_not_defined' => "Isn't Defined",
            'contain_any_of' => 'Contain any of',
            'does_not_contain_any_of' => "Doesn't contain any of",
            'is_equal_to' => 'Is equal to',
            'is_not_equal_to' => 'Is not equal to',
            'contain' => 'Contain',
            'does_not_contain' => "Doesn't contain",
            'active' => 'Active',
            'draft' => 'Draft'
        ];


        $filter['attributes'][] = [
            'label' => 'Default',
            'title' => 'default',
            'options' => [
                [
                    'label' => 'Categories',
                    'value' => json_encode([
                        'value' => 'categories',
                        'type' => 'multi_select',
                        'api_url' => "categories?tree_structure=true"
                    ]),
                ],
                [
                    'label' => 'Attribute Sets',
                    'value' => json_encode([
                        'value' => 'families',
                        'type' => 'multi_select',
                        'api_url' => "families"
                    ]),
                ],
                [
                    'label' => 'Brands',
                    'value' => json_encode([
                        'value' => 'brands',
                        'type' => 'multi_select',
                        'api_url' => "brands"
                    ]),
                ],
                [
                    'label' => 'Vendors',
                    'value' => json_encode([
                        'value' => 'vendors',
                        'type' => 'multi_select',
                        'api_url' => "vendors"
                    ]),
                ],
                [
                    'label' => 'Stores',
                    'value' => json_encode([
                        'value' => 'channels',
                        'type' => 'multi_select',
                        'api_url' => "channels"
                    ]),
                ],
                [
                    'label' => 'Languages',
                    'value' => json_encode([
                        'value' => 'versions',
                        'type' => 'multi_select',
                        'api_url' => "versions"
                    ]),
                ],
                [
                    'label' => 'Status',
                    'value' => json_encode([
                        'value' => 'status',
                        'type' => 'status'
                    ]),
                ],
                [
                    'label' => 'Assets',
                    'value' => json_encode([
                        'value' => 'assets',
                        'type' => 'assets'
                    ]),
                ],
                [
                    'label' => 'Product Identifier',
                    'value' => json_encode([
                        'value' => 'handle',
                        'type' => 'short_text'
                    ]),
                ],
                [
                    'label' => 'Variants',
                    'value' => json_encode([
                        'value' => 'variants',
                        'type' => 'variants'
                    ]),
                ]
            ],
        ];

        $filter['attributes'][] = [
            'label' => 'Variants',
            'title' => 'variants',
            'options' => [
                [
                    'label' => 'Variant Name',
                    'value' => json_encode([
                        'value' => 'name',
                        'type' => 'short_text',
                        'family' => 'variants',
                    ]),
                ],
                [
                    'label' => 'SKU',
                    'value' => json_encode([
                        'value' => 'sku',
                        'type' => 'short_text',
                        'family' => 'variants',
                    ]),
                ],
                [
                    'label' => 'UPC/EAN/ISBN ',
                    'value' => json_encode([
                        'value' => 'barcode',
                        'type' => 'short_text',
                        'family' => 'variants',
                    ]),
                ],
                [
                    'label' => 'Price',
                    'value' => json_encode([
                        'value' => 'price',
                        'type' => 'number',
                        'family' => 'variants',
                    ]),
                ],
                [
                    'label' => 'Cost Price',
                    'value' => json_encode([
                        'value' => 'cost_price',
                        'type' => 'number',
                        'family' => 'variants',
                    ]),
                ],
                [
                    'label' => 'Compare At Price',
                    'value' => json_encode([
                        'value' => 'compare_at_price',
                        'type' => 'number',
                        'family' => 'variants',
                    ]),
                ],
                [
                    'label' => 'Weight',
                    'value' => json_encode([
                        'value' => 'weight',
                        'type' => 'number',
                        'family' => 'variants',
                    ]),
                ],
            ],
        ];

        $families = Auth::user()->families()->get();

        foreach ($families as $family) {
            $family_flag = false;
            $family_attributes = [
                'label' => $family->name,
                'title' => $family->name,
            ];
            $attributes = $family->attributes;
            foreach ($attributes as $attribute) {
                if ($attribute->handle == 'product_name') {
                    $family_attributes['options'][] = [
                        'label' => $attribute->name,
                        'value' => json_encode([
                            'value' => $attribute->id,
                            'type' => 'short_text',
                            'json_data' => [
                                'attribute_id' => $attribute->id,
                                'family_id' => $family->id
                            ]
                        ]),
                    ];
                }
                elseif ($attribute->handle == 'description') {
                    $family_attributes['options'][] = [
                        'label' => $attribute->name,
                        'value' => json_encode([
                            'value' => $attribute->id,
                            'type' => 'long_text',
                            'json_data' => [
                                'attribute_id' => $attribute->id,
                                'family_id' => $family->id
                            ]
                        ]),
                    ];
                    $family_flag = true;
                    break;
                }

            }

            if ($family_flag) {
                $filter['attributes'][] = $family_attributes;
            }
        }


        $filter['formulas'] = [
            'multi_select' => [
                [
                    'label' => $type_hint['is_defined'],
                    'value' => 'is_defined'
                ],
                [
                    'label' => $type_hint['is_not_defined'],
                    'value' => 'is_not_defined'
                ],
                [
                    'label' => $type_hint['contain_any_of'],
                    'value' => 'contain_any_of'
                ],
                [
                    'label' => $type_hint['does_not_contain_any_of'],
                    'value' => 'does_not_contain_any_of'
                ]
            ],
            'dropdown' => [
                [
                    'label' => $type_hint['is_defined'],
                    'value' => 'is_defined'
                ],
                [
                    'label' => $type_hint['is_not_defined'],
                    'value' => 'is_not_defined'
                ],
                [
                    'label' => $type_hint['is_equal_to'],
                    'value' => 'is_equal_to'
                ],
                [
                    'label' => $type_hint['is_not_equal_to'],
                    'value' => 'is_not_equal_to'
                ]
            ],
            'short_text' => [
                [
                    'label' => $type_hint['is_defined'],
                    'value' => 'is_defined'
                ],
                [
                    'label' => $type_hint['is_not_defined'],
                    'value' => 'is_not_defined'
                ],
                [
                    'label' => $type_hint['contain'],
                    'value' => 'contain'
                ],
                [
                    'label' => $type_hint['does_not_contain'],
                    'value' => 'does_not_contain'
                ],
                [
                    'label' => $type_hint['is_equal_to'],
                    'value' => 'is_equal_to'
                ],
                [
                    'label' => $type_hint['is_not_equal_to'],
                    'value' => 'is_not_equal_to'
                ]
            ],
            'long_text' => [
                [
                    'label' => $type_hint['is_defined'],
                    'value' => 'is_defined'
                ],
                [
                    'label' => $type_hint['is_not_defined'],
                    'value' => 'is_not_defined'
                ],
                [
                    'label' => $type_hint['contain'],
                    'value' => 'contain'
                ],
                [
                    'label' => $type_hint['does_not_contain'],
                    'value' => 'does_not_contain'
                ],
                [
                    'label' => $type_hint['is_equal_to'],
                    'value' => 'is_equal_to'
                ],
                [
                    'label' => $type_hint['is_not_equal_to'],
                    'value' => 'is_not_equal_to'
                ]
            ],
            'number' => [
                [
                    'label' => $type_hint['is_defined'],
                    'value' => 'is_defined'
                ],
                [
                    'label' => $type_hint['is_not_defined'],
                    'value' => 'is_not_defined'
                ],
                [
                    'label' => $type_hint['contain'],
                    'value' => 'contain'
                ],
                [
                    'label' => $type_hint['does_not_contain'],
                    'value' => 'does_not_contain'
                ],
                [
                    'label' => $type_hint['is_equal_to'],
                    'value' => 'is_equal_to'
                ],
                [
                    'label' => $type_hint['is_not_equal_to'],
                    'value' => 'is_not_equal_to'
                ]
            ],
            'assets' => [
                [
                    'label' => $type_hint['is_defined'],
                    'value' => 'is_defined'
                ],
                [
                    'label' => $type_hint['is_not_defined'],
                    'value' => 'is_not_defined'
                ],
            ],
            'variants' => [
                [
                    'label' => $type_hint['is_defined'],
                    'value' => 'is_defined'
                ],
                [
                    'label' => $type_hint['is_not_defined'],
                    'value' => 'is_not_defined'
                ],
            ],
            'status' => [
                [
                    'label' => $type_hint['active'],
                    'value' => '1'
                ],
                [
                    'label' => $type_hint['draft'],
                    'value' => '0'
                ],
            ],
        ];


        return $filter;
    }
}
