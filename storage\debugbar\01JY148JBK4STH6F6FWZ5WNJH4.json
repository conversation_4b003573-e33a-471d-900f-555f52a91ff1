{"__meta": {"id": "01JY148JBK4STH6F6FWZ5WNJH4", "datetime": "2025-06-18 09:00:02", "utime": **********.805281, "method": "GET", "uri": "/api/2024-12/product-quality-score", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.170794, "end": **********.805311, "duration": 0.6345169544219971, "duration_str": "635ms", "measures": [{"label": "Booting", "start": **********.170794, "relative_start": 0, "end": **********.71324, "relative_end": **********.71324, "duration": 0.****************, "duration_str": "542ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.713254, "relative_start": 0.****************, "end": **********.805314, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "92.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.729937, "relative_start": 0.**************, "end": **********.734782, "relative_end": **********.734782, "duration": 0.0048449039459228516, "duration_str": "4.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.798308, "relative_start": 0.****************, "end": **********.798928, "relative_end": **********.798928, "duration": 0.0006201267242431641, "duration_str": "620μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.801136, "relative_start": 0.****************, "end": **********.801283, "relative_end": **********.801283, "duration": 0.0001468658447265625, "duration_str": "147μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/product-quality-score", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:128-155</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00859, "accumulated_duration_str": "8.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7579079, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 43.306}, {"sql": "select\nSUM(CASE WHEN mean_score >= 90 THEN 1 ELSE 0 END) AS good,\nSUM(CASE WHEN mean_score >= 50 AND mean_score < 90 THEN 1 ELSE 0 END) AS fair,\nSUM(CASE WHEN mean_score < 50 THEN 1 ELSE 0 END) AS bad\nfrom (select products.id, AVG(product_version.score) as mean_score from `products` inner join `product_version` on `products`.`id` = `product_version`.`product_id` where `organization_id` = 1 group by `products`.`id`) as avg_scores limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 145}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.785013, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:145", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=145", "ajax": false, "filename": "DashboardController.php", "line": "145"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.306, "width_percent": 56.694}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sfbzWZ5Cja58au7BqBi4fsmikZRS0AgmfG3THjNr", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/product-quality-score\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "organization_id": "1", "password_hash_web": "null"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/product-quality-score", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore", "uri": "GET api/2024-12/product-quality-score", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=128\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:128-155</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f2ee2ac-b8e7-4044-abe4-ae52d36c8dcd\" target=\"_blank\">View in Telescope</a>", "duration": "637ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1029520584 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1029520584\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-966768075 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-966768075\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-222791464 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkRXTVgraU5kVWt2QXczTFFnMmJXblE9PSIsInZhbHVlIjoiUmJRZTVxK1U0OWp0bVdXQ0pPL2hKMmYxV2d3OGUybXdRdmFxRUJWUHdINE5EcmxTcjdpNXcyWm1Obmp6bFpQeHliSUdsMkhTUUhpYWdFV2R0dFR6ZjRFbnR0UHlqYTFUd1NmbGVMdWpybUpzYVJkRmhaZnk2QVhpTTBYUUJtSVEiLCJtYWMiOiI0NWNkZDE1ZmZlYjI2M2ZiYjA5YTg1MjE5Y2M4YzQwMTRmYWEyODZhZjQ1YzlmYTQwOTJjNGQxZjU1OTM4ZGE3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IllaQk9RYkRpV0tUREZCb2FsemFmeGc9PSIsInZhbHVlIjoiZkVTNmgxa0JkTTlNK28zVFNWOTZIZW5kWTVad21Td2lHRlkzTXJ6V0ZwOTB4eXZFUUo1Z1pUUjNPWjUrQVl2YnpTdm1pbU9FNXV1eFRzVGdHOStwNUlmMWZSR2o5ZEZhU01MN2hNamVXUno5QW8yLy8wTXEzWU4vdDF2ZDhMSjIzTkg3RVJWQnFxRVpJZUhkTG05Tkd3PT0iLCJtYWMiOiJmMDgwYzM2NzcxOGJhNjAyZWQxZjFkYWQ2NDg2NjBkYzg5ZTJiMTZiZmMzYzQ0ZTZjNTUxMTczOTYyZGQ4NDA3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkRXTVgraU5kVWt2QXczTFFnMmJXblE9PSIsInZhbHVlIjoiUmJRZTVxK1U0OWp0bVdXQ0pPL2hKMmYxV2d3OGUybXdRdmFxRUJWUHdINE5EcmxTcjdpNXcyWm1Obmp6bFpQeHliSUdsMkhTUUhpYWdFV2R0dFR6ZjRFbnR0UHlqYTFUd1NmbGVMdWpybUpzYVJkRmhaZnk2QVhpTTBYUUJtSVEiLCJtYWMiOiI0NWNkZDE1ZmZlYjI2M2ZiYjA5YTg1MjE5Y2M4YzQwMTRmYWEyODZhZjQ1YzlmYTQwOTJjNGQxZjU1OTM4ZGE3IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkxhVFpPNU83TUpXd2p1UHErakJMY2c9PSIsInZhbHVlIjoiNGMyckpFcUFmUUZhc2hoL1lxUE9ncGp4UVpDRUI5QjZ2ZTlMbzNNYUpPUkZpU3ovbWxPajVTeXhoc09WSnQvNzBCdzVMOWtnZVVQdFZiUDdURzVhQjRGN2Y5NUNubnpIZ3Z3U1ZXSWh5cnBTa1F0WDNWY2x2bzZONWZ4ZWNHUnEiLCJtYWMiOiJmYzU2OGU3MzE5Njc2NWM0MTIyZTI5MjkwNTI4YzA1ZTljMWU1ZDk5YmJmMDQ3Mjg5MjA1YWFiODhiMzk1OGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222791464\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|5wivf0aEdBU2DcxsT7ukeLYOGe22ZuevP2HYO4YKzxdIUp4dIj09d1gADg3t|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sfbzWZ5Cja58au7BqBi4fsmikZRS0AgmfG3THjNr</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zOG6naMg5XWtD9AouQ0M7oAC4kl16NZA3hyAuWL6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1994663108 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 09:00:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjFDTlpDMU5tZUtFcFdaQyt1RlYwdHc9PSIsInZhbHVlIjoieEM2OUZqejB3MC9QRTR5bXJaOThaUEQ0VERXNC8zMHo1UG9uNHpObW9ZR1ZwMmdMYzBidTZQZ1dzdkFQK2grRFViRjVhYWdlOVduMC9BRnBvTVNjMmFBV0U2Q3JJUUc2Sm82RXRUNmh2bi9PVmdybzFHUisrRXBOMHYrMy82N1IiLCJtYWMiOiIxMDFhYjgzYTM0YTAyMmJmN2RkODUwZmIyOWM3NDcwZDk5ZmJlNmM1ZmJlMWY4ZTZlYTA3OTgzMmY3YTRjMzcyIiwidGFnIjoiIn0%3D; expires=Wed, 18 Jun 2025 11:00:02 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6Iklta2Q0TmQwUnJmd3QwQWNGZEJaR1E9PSIsInZhbHVlIjoiWlBFdUlWVGd4UDlVM3VhekRDWTBFQmNUV1daNkpVL0IxejNmdnc1c0RWbE1tMlVHek84OThFa0tKNHlPSzlFbmdHVThsN1JZWTIvSXVoKzJsOEJBMHVRcFpzL0tzZjhjcnlyb3ZYa1U1dmZHbEhRV0E2YlpjN0VwWmk5L2lnU3IiLCJtYWMiOiJlODRlYTFkMTgzMWZkMmJhMzEwNWFiZDFhZDI4ZWFkNzYzY2ViY2NiNzI2MmI3M2Q1NThiMDRlMTAyMGY2NGQyIiwidGFnIjoiIn0%3D; expires=Wed, 18 Jun 2025 11:00:02 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjFDTlpDMU5tZUtFcFdaQyt1RlYwdHc9PSIsInZhbHVlIjoieEM2OUZqejB3MC9QRTR5bXJaOThaUEQ0VERXNC8zMHo1UG9uNHpObW9ZR1ZwMmdMYzBidTZQZ1dzdkFQK2grRFViRjVhYWdlOVduMC9BRnBvTVNjMmFBV0U2Q3JJUUc2Sm82RXRUNmh2bi9PVmdybzFHUisrRXBOMHYrMy82N1IiLCJtYWMiOiIxMDFhYjgzYTM0YTAyMmJmN2RkODUwZmIyOWM3NDcwZDk5ZmJlNmM1ZmJlMWY4ZTZlYTA3OTgzMmY3YTRjMzcyIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 11:00:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6Iklta2Q0TmQwUnJmd3QwQWNGZEJaR1E9PSIsInZhbHVlIjoiWlBFdUlWVGd4UDlVM3VhekRDWTBFQmNUV1daNkpVL0IxejNmdnc1c0RWbE1tMlVHek84OThFa0tKNHlPSzlFbmdHVThsN1JZWTIvSXVoKzJsOEJBMHVRcFpzL0tzZjhjcnlyb3ZYa1U1dmZHbEhRV0E2YlpjN0VwWmk5L2lnU3IiLCJtYWMiOiJlODRlYTFkMTgzMWZkMmJhMzEwNWFiZDFhZDI4ZWFkNzYzY2ViY2NiNzI2MmI3M2Q1NThiMDRlMTAyMGY2NGQyIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 11:00:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994663108\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2024395132 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sfbzWZ5Cja58au7BqBi4fsmikZRS0AgmfG3THjNr</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/api/2024-12/product-quality-score</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024395132\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/product-quality-score", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore"}, "badge": null}}