<?php

namespace App\Jobs\Export;

use App\Classes\Export\ExportToCsv;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GetProductIdsChunkArray implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $data) {}

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        $export_to_csv_obj = new ExportToCsv();
        if ($this->data['data']['request_data']['temp_name'] == "shopify_default" || $this->data['data']['request_data']['temp_name'] == "magento_default") {
            $export_to_csv_obj->export_to_csv(
                $this->data['data']['request_data']['nodes'],
                function ($error) {
                    Log::error($error);
                },
                function ($success) {
                    return $success;
                }
            );
        } else {
            $export_to_csv_obj->get_products_chunk_array($this->data['products'], $this->data['product_obj'], $this->data['data']);
        }
    }

    public function get_final_products()
    {
        return $this->final_products;
    }
}
