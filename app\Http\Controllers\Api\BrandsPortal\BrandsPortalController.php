<?php

namespace App\Http\Controllers\Api\BrandsPortal;

use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;
use App\Models\BrandsPortal;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreBrandsPortalRequest;
use App\Classes\Mapping\Conversion;
use App\Models\Channel\Channel;
use App\Models\Product\Template;
use Apimio\Gallery\Models\File;
use App\Traits\Api\ApiResponseTrait;

use Apimio\MappingConnectorPackage\models\Template as PackageTemplate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use App\Traits\Api\BrandPortal\ApiResponseFilterTrait;
use Laravel\Sanctum\NewAccessToken;

class BrandsPortalController extends Controller
{
    use ApiResponseTrait, ApiResponseFilterTrait;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum")->except('downloadProducts');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $BrandPortals = $request->user()->brandsPortals()->with('channels', 'templates');
        // Get the paginated data
        $paginatedBrandPortals = $BrandPortals->paginate($request->get("paginate") ?? 10, ['*'], 'page', $request->get("page") ?? 1);

        $pagination = $this->formatPagination($paginatedBrandPortals);

        // Prepare pagination details
        return response(
            [
                'message' =>            'Brand portals retrieved successfully',
                'brandPortals' =>  $paginatedBrandPortals->toArray()['data'],
                'pagination' => $pagination
            ]
        );
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $stores = Channel::all();
        $organization_id = (auth()->user()->organization_id ?? auth()->user()->id)?? null ;
        //templates with type export
        $databasetemplates = Template::where('type', 'export')->get();
        $templates = $this->getTemplatesFromMapping($databasetemplates);

        return view('brandportal.brandportal', compact('stores', 'templates', 'organization_id'));
    }

    public function getTemplatesFromMapping($templates)
    {
        $mapping = new MappingFieldController();
        $data['left_array'] = PackageTemplate::apimio_mapping_array([], false);
        $data['right_array'] = [
            "array_name" => "Export",
            "nodes" => []
        ];
        $data_required = [
            'organization_id' => auth()->user()->organization_id,
            'output_type' => 'export',
            'template_method_type' => 'export', //its only contain ('import','export','shopify','clone','other')
            'sync' => false,
            'redirect_url_route' => 'products.export', //it should be post method
            'versions' => Conversion::fetch_all_version_in_array(),
            'catalogs' => Conversion::fetch_all_catalog_in_array(),
            // 'locations' => $location_array,
            'filter_query' => $filters ?? [],

            //testing product
            // 'products' => $product,

        ];
        $defaults = PackageTemplate::default_template(
            $data_required,
            function ($error) {
                return false;
            },
            function ($success) {
                return $success;
            }
        );
        $allTemplates = $defaults ? $defaults->merge($templates) : [];
        return $allTemplates->toArray();
    }
    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBrandsPortalRequest $request)
    {
        // add orgnaization_id in session
        auth()->user()->organization_id = $request->user()->id;

            // Handle file upload using the set_data method from the File model
            $file = new File();
            $file->organization_id = $request->user()->id;
            if (isset($request->file)) {
                $file_model = $file->save_file($request->file);
                $folder = $file->get_default_folder();
                $folder->files()->attach($file_model->id);
            }

            $data = $request->validated();
            $data['file_id'] = isset($file_model) ? $file_model->id : null;

            $brandsPortal = $request->user()->brandsPortals()->create($data);

            // Attach stores and templates after saving
            $brandsPortal->channels()->attach($request->store);
            if (isset($request->template[0])) {
                $template = json_decode($request->template[0]);
                if ($template != "shopify_default" || $template != "magento_default") {
                    $brandsPortal->templates()->attach($template);
                }
            }

            //update brandportal
            $brandsPortal->update(['url' => $this->generate_url($brandsPortal->id)]);

            return  response(['message' =>
                'Brand portal created successfully.',
                'brandsPortal'=>$brandsPortal],
            );

    }

    private function generate_url($id)
    {
        $hash = urlencode(encrypt($id));
        $url  = env('APP_URL') . '/brandportal/show/' . $hash;
        return $url;
    }

    /**
     * Display the specified resource.
     */
    public function show($hash)
    {
        $id = (int) urldecode(decrypt($hash));
        $brandsPortal = auth()->user()->brandsPortals()->find($id);
        $organizationId = auth()->user()->id;

        // get a token for the authenticated user
        $tokenObj = auth()->user()->currentAccessToken();
        $token = ( new NewAccessToken($tokenObj, $tokenObj->getKey().'|'.$tokenObj->token))->plainTextToken;
        $templates = $brandsPortal->templates->select('name', 'id');
        return view('brandportal.publicview', compact('brandsPortal', 'token', 'templates', 'organizationId'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BrandsPortal $brandsPortal)
    {
        $stores = Channel::all();
        $templates = Template::all();
        return view('brandportal.brandportal', compact('brandsPortal', 'stores', 'templates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update($id, Request $request)//StoreBrandsPortalRequest
    {
        $brandsPortal = $request->user()->brandsPortals()->findOrFail($id);
        $brandsPortal->update($request->all());

        $oldFile = File::find($brandsPortal->file_id);
            if (isset($request->file)) {
                // Handle file upload using the set_data method from the File model
                $file = new File();
                $file_model = $file->save_file($request->file);
                $folder = $file->get_default_folder();
                $folder->files()->attach($file_model->id);
                $brandsPortal->file_id = $file_model->id;
                $brandsPortal->logo_url = $file->link;
                $oldFile->delete_file();
            }
            $brandsPortal->save();

            // Attach stores and templates after saving
            $brandsPortal->channels()->attach($request->store);
            $brandsPortal->templates()->attach($request->template);
            // $brandsPortal->url = $this->generate_url($brandsPortal->id);

            return  response([
                'message' => 'Brand portal created successfully.',
                'brandsPortal'=> $brandsPortal
                ],
            );
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BrandsPortal $brandsPortal)
    {
        $brandsPortal->delete();
        return $this->successResponse('Brand portal deleted successfully.', $brandsPortal, 200);
    }

    public function downloadProducts(Request $request)
    {
        $filename = $request->query('filename');
        $portal = $request->query('portal');
        BrandsPortal::findOrFail((int) urldecode(decrypt($portal)));
        $fileUrl = Storage::disk('s3')->url($filename);

        $download = [
            'file' => $fileUrl,
            'name' => basename($fileUrl),
            'details' => "Your file is ready for download. We've successfully generated your export, and you can now access it by clicking the button below."
        ];

        return view('download', compact('download'));
    }
}
