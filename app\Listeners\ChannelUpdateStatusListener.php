<?php

namespace App\Listeners;

use App\Models\Channel\ChannelProduct;
use App\Events\ChannelUpdateStatusEvent;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\Channel\ChannelProductStatus;

class ChannelUpdateStatusListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\ChannelUpdateStatusEvent  $event
     * @return void
     */
    public function handle(ChannelUpdateStatusEvent $event)
    {
        $product = $event->product->load(['channels']);
        if (isset($product->channels)) {

            foreach ($product->channels as $channel) {

                $productStatus =  ChannelProductStatus::where([
                    "channel_product_id" => $channel->pivot->id,
                    "organization_id" => $product->organization_id,
                    "type" => 'shopify',
                ])->first();

                if (!$productStatus) {
                    $productStatus = new ChannelProductStatus();
                    $productStatus->response =  isset($event->data["shopify_product_id"]) ?  ['sync_id' => $event->data["shopify_product_id"]] : null;
                }

                $productStatus->channel_product_id = $channel->pivot->id;
                $productStatus->organization_id = $product->organization_id;
                $productStatus->type = "shopify";
                $productStatus->status = $event->isUpdated ? 0 : 1;
                $productStatus->save();
            }
        }
    }
}
