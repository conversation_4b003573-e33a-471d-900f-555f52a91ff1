<?php

namespace App\Classes\Shopify;

use Apimio\Completeness\Events\CalculateScore;
use App\Events\ChannelUpdateStatusEvent;
use App\Events\Product\ManageChannels;
use App\Events\Product\ManageFamilyAttributes;
use App\Events\Product\ManageFiles;
use App\Events\Product\ManageInvites;
use App\Events\Product\ManageVariants;
use App\Events\Product\ManageVersion;
use App\Models\Channel\ChannelVersion;
use App\Models\Notification\ErrorLog;
use App\Models\Product\Category;
use App\Models\Product\CategoryProduct;
use App\Models\Product\Product;
use App\Providers\ProductServiceProvider;
use Exception;
use Illuminate\Support\Facades\Log;

class StoreProductToApimioByGQL
{
    private $productObj;
    public function __construct(public array $product , public int $channel_id , public int $organization_id , public mixed $version_id = null)
    {
        $this->productObj = new Product();
        if(!$this->version_id){
            $this->getVersionFromChannel();
        }
    }

    public function getVersionFromChannel(){
        $this->version_id =  ChannelVersion::query()
            ->select('version_id')
            ->where('channel_id',$this->channel_id)
            ->first()
            ?->version_id;
    }

    /**
     * @throws Throwable
     */
    public function store(callable $success)
    {
        $productObj = null;
        Log::channel('shopify')->info('Storing product', [$this->product]);
        try {
            $product_update_flag = false ;
            $productObj = $this->productObj::query()
                ->where('sku', $this->product['handle'])
                ->where('organization_id', $this->organization_id)
                ->first();
            if (!$productObj) {
                $product_update_flag = true;
                $productObj = $this->productObj->Create([
                    'sku' => $this->product['handle'],
                    'organization_id' => $this->organization_id,
                    'status' => ($this->product['status'] === 'ACTIVE') ? 1 : 0,
                ]);
            } else {
                $data = [
                    'organization_id' => $this->organization_id,
                    'description' => "Product already exist in our system <b> handle : {$this->productObj->handle} </b>. We are updating the existing product.",
                    'type' => 'shopify',
                    'link' => route('products.edit', $productObj->id),
                    'link_text' => 'View Product',
                    'status' => 'info',
                ];

                (new ErrorLog())->saveErrorLog($data);
                Log::channel('shopify')->info('Product already exist in our system', [$this->product['handle']]);
            }
            info('Product created successfully', [$productObj->id]);
           // dd($productObj);
           // dump($this->transformVariants($this->product['variants']['edges']));
            $eventsArray[] = new ManageFiles($productObj, $this->transformImages($this->product['media']['edges']), $this->channel_id, true);
            $eventsArray[] = new ManageInvites($productObj, [['name'=> $this->product['vendor']]], $this->channel_id, true);
            $eventsArray[] = new ManageVersion($productObj, [['id' => $this->version_id]]);
            $eventsArray[] = new ManageChannels($productObj, array(['id' => $this->channel_id]) );

          $eventsArray[] = new ManageVariants(
                $productObj,
                $this->transformVariants($this->product['variants']['edges']),
                [
                    'versions' => [['id' => $this->version_id]],
                    'channel_id' => $this->channel_id
                ]);

            $eventsArray[] = new ManageFamilyAttributes($productObj, $this->createGeneralFamilyAttributeArray(), [['id' => $this->version_id]]);

            $eventsArray[] = new ChannelUpdateStatusEvent(
                product: $productObj,
                data: ['shopify_product_id' => $this->extractId($this->product['id'])]
            );

            ProductServiceProvider::trigger($eventsArray);

            $this->accessCollection($productObj->id);

           event(new CalculateScore($productObj));

        } catch (Exception $e) {
            Log::channel('shopify')->error('Error while saving product', [$e->getMessage()]);
//            $data = [
//                'organization_id' => $this->organization_id,
//                'description' => "There is an error while saving product <b> sku : {$productObj->sku} </b>.<br>  {$e->getMessage()}",
//                'type' => 'shopify',
//                'link' => route('products.edit', $productObj->id),
//                'link_text' => 'View Product',
//                'status' => 'error',
//            ];
//            (new ErrorLog())->saveErrorLog($data);
            Log::error([$e->getMessage()]);
            if($product_update_flag){
                Product::where('id', $productObj->id)->delete();
            }
        }
        return $success($productObj);
    }

    public function createGeneralFamilyAttributeArray() {
        return [
            [
                "name"=>"General",
                "attributes"=>[
                    [
                        "name"=>"Product Name",
                        'handle'=>"product_name",
                        'attribute_type_id'=>1,
                        "value" => $this->product['title'],
                    ],
                    [
                        "name"=>"Description",
                        'handle'=>"description",
                        'attribute_type_id'=>3,
                        "value" => $this->product['descriptionHtml'],
                    ]
                ]
            ],
            [
                "name"=>"SEO",
                "attributes"=>[
                    [
                        'handle'=>"seo_keyword",
                        'name'=>'Tags',
                        'attribute_type_id'=>1,
                        "value" => implode(',', $this->product['tags']),
                    ],
                    [
                        'name'=>"URL Slug",
                        'handle'=>"seo_url",
                        'attribute_type_id'=>11,
                        "value" => $this->product['handle'],
                    ]
                ]
            ]
        ];
    }

    protected function transformOptions(array $options): array
    {
        $transformed = [];
        foreach ($options as $option) {
            $transformed[] = [
                'shopify_id'     => $this->extractId($option['id'] ?? ''),
                'name'   => $option['name'] ?? null,
                'options' => $option['values'] ?? [],
            ];
        }
        return $transformed;
    }

    protected function transformImages(array $mediaEdges): array
    {
        $images = [];
        foreach ($mediaEdges as $edge) {
            if (!isset($edge['node'])) {
                continue;
            }
            $node = $edge['node'];
            $image = $node['image'] ?? [];
            $images[] = [
                'shopify_image_id' => $this->extractId($node['id'] ?? ''),
                'link'             => $image['url'] ?? null,
                'width'            => $image['width'] ?? null,
                'height'           => $image['height'] ?? null,
            ];
        }
        return $images;
    }


    /**
     * Convert variants to desired structure.
     *
     * @param array $variantsEdges
     * @return array
     */
    protected function transformVariants(array $variantsEdges): array
    {
        $variants = [];

        foreach ($variantsEdges as $edge) {
            if (!isset($edge['node'])) {
                continue;
            }
            $variant = $edge['node'];

            // Prepare attributes and options from selectedOptions (if they exist)
            $attributes = [];
            $options = [];
            if (isset($variant['selectedOptions']) && is_array($variant['selectedOptions'])) {
                foreach ($variant['selectedOptions'] as $option) {
                    $attributes[] = [
                        'name'  => $option['name'] ?? null,
                        'value' => $option['value'] ?? null,
                    ];
                    $options[] = $option['value'] ?? null;
                }
            }

            // Build files from the variant's image if available.
            $files = [];
            if (isset($variant['image']) && is_array($variant['image'])) {
                $files[] = [
                    'shopify_id'     => $this->extractId($variant['image']['id'] ?? ''),
                    'link'   => $variant['image']['src'] ?? null,
                    'width'  => $variant['image']['width'] ?? null,
                    'height' => $variant['image']['height'] ?? null,
                ];
            }
            // If no image is available, you can leave $files as an empty array or handle it as needed.

            // Build each variant structure
            $attributes = $this->transformOptions($this->product['options']);
            $variants['attributes'] = $this->product['options']? $attributes : [] ;
            $variants['data'][] = [
                'id'               => $this->extractId($variant['id'] ?? ''),
                'attributes'       => $this->product['options']? $attributes : [] ,
                'options'          => $options,
                'name'             => $variant['title'] ?? null,
                'sku'              => $variant['sku'] ?? null,
                'price'            => $variant['price'] ?? null,
                'weight'           => $variant['weight'] ?? null,
                'weight_unit'      => $variant['weight_unit'] ?? null,
                'cost_price'       => $variant['cost_price'] ?? null,
                'compare_at_price' => $variant['compareAtPrice'] ?? null,
                'quantity'         => $variant['inventoryQuantity'] ?? null,
                'barcode'          => $variant['barcode'] ?? null,
                'track_quantity'   => (isset($variant['inventory_management']) && $variant['inventory_management'] === 'shopify') ? 1 : 0,
                'continue_selling' => (isset($variant['inventory_policy']) && $variant['inventory_policy'] === 'continue') ? 1 : 0,
                'files'            => $files,
                'inventory_id'     => isset($variant['inventoryItem']['id']) ? $this->extractId($variant['inventoryItem']['id']) : '',
            ];
        }
        return $variants;
    }


    public function accessCollection($product_id): void
    {
        $categoryIds = [];
        foreach($this->product['collections']['edges'] as $collect){
                $collection_id = $this->extractId($collect['node']['id']);
                Log::channel('shopify')->info("collection_id ssss");
                Log::channel('shopify')->info([$collection_id]);
                $cat = Category::query()
                    ->where('organization_id',$this->organization_id)
                    ->with('channel')
                    ->whereHas('channel',function ($q) use ($collection_id) {
                        $q->where('channel_id',$this->channel_id)
                            ->where('store_connect_type','shopify')
                            ->where('store_connect_id',$collection_id);
                    })
                    ->first();
                if($cat){
                    $categoryIds[] = $cat->id;
                    $categoryProduct = CategoryProduct::query()
                        ->where('product_id',$product_id)
                        ->where('category_id',$cat->id)
                        ->first();
                        Log::channel('shopify')->info("categoryProduct ssss");
                        Log::channel('shopify')->info([$categoryProduct?->id]);
                    if(!$categoryProduct){
                        CategoryProduct::query()->create([
                            'product_id' => $product_id,
                            'category_id' => $cat->id
                        ]);
                    }
                }else{
                    Log::channel('shopify')->info('Collection not link with product');
                }
        }
        Log::channel('shopify')->info("categoryId ssss");
        Log::channel('shopify')->info([$categoryIds]);
        if(!empty($categoryIds)){
            CategoryProduct::query()->where('product_id',$product_id)->whereNotIn('category_id',$categoryIds)->delete();
        }


    }

    protected function extractId(string $gid): string
    {
        $parts = explode('/', $gid);
        return end($parts);
    }

}
