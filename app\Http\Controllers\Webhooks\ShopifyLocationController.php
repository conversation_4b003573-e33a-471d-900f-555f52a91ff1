<?php

namespace App\Http\Controllers\Webhooks;

use Illuminate\Http\Request;
use App\Models\Location\Location;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Classes\Shopify\locationShopify;
use App\Jobs\Shopify\Webhooks\Location\UpdateLocationJob;

class ShopifyLocationController extends ShopifyWebhookController
{
    public function createLocation(Request $request){
        $this->createOrUpdateLocation($request->all());
    }

    public function updateLocation(Request $request){
        $this->createOrUpdateLocation($request->all());
    }

    public function deleteLocation(Request $request){
        try {
        $shopify_channel = $this->getShopifyChannelDetail();
        if(!$shopify_channel){
            Log::channel('webhook')->error(['message' => 'Shopify channel not found']);
        }
        $channel_id = $shopify_channel->channel_id;
        $channel = $this->getChannelDetails($channel_id);
        dispatch(new UpdateLocationJob($channel, $request->only('id')));
        return "success";
        // if(!$channel){
        //     Log::channel('webhook')->error(['message' => 'Apimio channel not found']);
        // }
        // $location = Location::query()
        // ->where(['organization_id' => $channel->organization_id])
        // ->whereHas("channelLocation", function($query) use ($channel, $request){
        //     $query->where(["channel_id" => $channel->id , "store_connect_id" => $request->id]);
        // })
        // ->first();
        // if(!$location){
        //     Log::channel('webhook')->error(['message' => 'Apimio location not found']);
        // }
        // $location->delete();
        // Log::channel('webhook')->info("Location has been deleted");
    } catch (\Exception $e) {
        Log::channel('webhook')->error($e);
    }

    }

    public function createOrUpdateLocation($loc){
        try {
            $shopify_channel = $this->getShopifyChannelDetail();
            if(!$shopify_channel){
                Log::channel('webhook')->error(['message' => 'Shopify channel not found']);
            }
            $channel_id = $shopify_channel->channel_id;
            $channel = $this->getChannelDetails($channel_id);
            dispatch(new UpdateLocationJob($channel, $loc));
            return "success";
            // if(!$channel){
            //     Log::channel('webhook')->error(['message' => 'Apimio channel not found']);
            // }
            // $data = array();
            // $data['name'] = $loc['name'];
            // $data['address'] = $loc['address1'];
            // $data['apartment'] = $loc['address2'];
            // $data['postal_code'] = $loc['zip'];
            // $data['city'] = $loc['city'];
            // $data['fulfill_online_orders'] = 1;
            // $data['phone_number'] = $loc['phone'];
            // $data['default_location'] = false;
            // $data['store_type'] = 'shopify';
            // $data['store_connect_id'] = $loc['id'];
            // $data['organization_id'] = $channel->organization_id;
            // $location = Location::query()
            // ->where(['organization_id' => $channel->organization_id])
            // ->whereHas("channelLocation", function($query) use ($channel, $loc){
            //     $query->where(["channel_id" => $channel->id , "store_connect_id" => $loc['id']]);
            // })
            // ->first();
            // if ($location) {
            //     $data['id'] = $location->id;
            //     if ($location->default_location == 1) {
            //         $data['default_location'] = true;
            //     }
            //     event(new \App\Events\LocationEvent($channel, [$data], true));
            //     Log::channel('webhook')->info("Location has been updated");
            // } else {
            //     event(new \App\Events\LocationEvent($channel, [$data]));
            //     Log::channel('webhook')->info("Location has been created");
            // }

        } catch (\Exception $e) {
            Log::channel('webhook')->error($e);
        }
    }
}
