<?php

namespace App\Http\Controllers\Product;

use App\Classes\Bulk\BulkEdit;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Product\File;
use App\Jobs\Bulk\BulkAssignQueue;
use App\Models\BatchProgress;
use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Product\Brand;
use App\Models\Product\Category;
use App\Models\Product\Family;
use App\Models\Product\Version;
use App\Models\Schedule\Task;
use Illuminate\Bus\Batch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;
use App\Models\Product\Attribute;
use App\Models\Filter;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use Inertia\Inertia;
class BulkEditController extends Controller
{

    // BULK ASSIGN FUNCTIONS
    /**
     * @param $data
     * @return array
     */
    protected function attributes_selected_option_filteration($data): array
    {
        $selected_options = $data['selected_options'] ? explode(',', $data['selected_options']) : [];
        $attributes = $data['attributes'] ?? [];
        $finalArray = [];
        foreach ($selected_options as $key) {
            if (array_key_exists($key, $attributes)) {
                $finalArray[$key] = $attributes[$key];
            }
        }
        return $finalArray;
    }

    public function bulkedit()
    {
        // return view('products.bulk-edit.index');
        return Inertia::render('resources/js/Pages/BulkEdit', [
            'bulk_edit' => $bulk_edit,
        ]);
    }


    function bulk_assign(Request $request, $scheduler = false)
    {

        try {

            if (!$scheduler) {
                // Check if required_data exists, otherwise initialize it as an array
                $requiredData = $request->get('required_data', []);
                $requiredData['organization_id'] = (string) auth()->user()->organization_id;
                $requiredData['user_id'] = auth()->user()->id;
                $request->merge(['required_data' => $requiredData]);
            }

            $bulk_edit_obj = new BulkEdit();
            $data = $request->all();


            // Check if this is a scheduler task
            if (isset($data['edit_task']) && $data['edit_task'] == "update"){
                (new TaskController())->store($request);

                $msg = 'Task updated successfully.';
                return Redirect::route("products.tasks")->withSuccess($msg);
            }
            elseif (!$scheduler && isset($data['required_data']['is_schedule']) && isset($data['required_data']['scheduled_at']) && isset($data['required_data']['timezone'])) {

                (new TaskController())->store($request);

                $msg = 'Bulk Assign scheduled successfully. It will start at the selected time.';
                return Redirect::route("products.index")->withSuccess($msg);
            }

            $batch_bulk_assign = Bus::batch([])
                ->then(function (Batch $batch) {
                    Log::info("bulk assign job done");
                })->dispatch();
            $data['required_data']['batch'] = $batch_bulk_assign->id;
            $bulk_edit_obj->send_notification_for_bulk_assign($data);

            // below code is for batch progress monitoring
            $batch_progress_data = [
                'user_id' => $data['required_data']['user_id'],
                'organization_id' => $data['required_data']['organization_id'],
                'batch_id' => $batch_bulk_assign->id ?? null,
                'type' => 'bulk_assign'
            ];
            (new BatchProgress())->store($batch_progress_data);

            //////////////////////////////////////below code is for without queue testing do not remove this code//////////////////////////////////

//                        $bulk_edit_obj->set_data($data)->products_bulk_assign(
//                            function ($error) use ($data){
//                                Log::error("Bulk Assign Queue error for user ". $data['required_data']['user']->id);
//                                Log::error($error);
//                            },
//                            function ($success){
//                                dd($success);
//                            }
//                        );


            BulkAssignQueue::dispatch($data);
            $msg = 'Bulk Assign started successfully. Refresh products page after some time.';
            return Redirect::route("products.index")->withSuccess($msg);
        } catch (\Exception $e) {
            Log::error(["main" => $e->getMessage()]);
            return Redirect::back()->withErrors("Something went wrong.! Please try again later.");
        }
    }


    /**
     * This is used on bulk assign modal trigger from ajax
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function bulk_assign_modal_generate(Request $request): \Illuminate\Http\JsonResponse
    {

        try {
            $bulk_data_array = $this->default_attribute_value_creation(auth()->user()->organization_id);
            $bulk_assign_action = $request->bulk_assign_action ?? '';

            $output = View::make("components.products.bulk.bulk-assign-modal")
                ->with('bulk_data_array', $bulk_data_array)
                ->with('bulk_assign_action', $bulk_assign_action)
                ->render();
            return response()->json(['output' => $output]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }

    function bulk_price_update_modal_generate(Request $request): \Illuminate\Http\JsonResponse
    {

        try {
            $task_id = $request->task_id ?? '';

            $task = null;

            $all_versions = Version::get()->pluck('name', 'id')->toArray();

            if ($task_id){
                $task = Task::withTrashed()->where('id' , $task_id)->first();
            }
            $output = View::make("components.products.bulk.bulk-price-update-modal")
                ->with('all_versions', $all_versions)
                ->with('task', $task)
                ->render();
            return response()->json(['output' => $output]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }




    /**
     * Generate default value of bulk assign attributes
     * @param $organization_id
     * @return array
     */
    public function default_attribute_value_creation($organization_id): array
    {

        $bulk_data_array = array();

        // version
        $bulk_data_array['versions'] = Version::where('organization_id', $organization_id)->pluck('name', 'id')->toArray();

        // categories
        $bulk_data_array['categories'] = Category::where('organization_id', $organization_id)->pluck('name', 'id')->toArray();

        // attribute_sets
        $bulk_data_array['attribute_sets'] = Family::select('id', 'name')->withCount('attributes')->whereNotIn('name', ['General', 'SEO'])->where('organization_id', $organization_id)->get()->toArray();

        // brands
        $bulk_data_array['brands'] = Brand::where('organization_id', $organization_id)->pluck('name', 'id')->toArray();

        // brands
        $bulk_data_array['stores'] = Channel::where('organization_id', $organization_id)->pluck('name', 'id')->toArray();

        // vendors
        $bulk_data_array['vendors'] = Invite::select('*', DB::raw("TRIM(CONCAT(COALESCE(invites.fname,''), ' ', COALESCE(invites.lname,''))) as full_name"))->where('organization_id_sender', $organization_id)->pluck('full_name', 'id')->toArray();
        $bulk_data_array['vendors'] = array_filter($bulk_data_array['vendors']);

        return $bulk_data_array;
    }





    // BULK EDIT FUNCTIONS

    /**
     * Fetches Apimio products.
     *
     * @param Request $request The request object.
     * @return Response The response object.
     */
    public function fetch_apimio_products(Request $request)
    {
        if ($request->isMethod('post')) {
            $data['target_page'] = $request->input('target_page', 1);
            $data['version_id'] = $request->input('bulk_edit_version_id', 1);
            $data['productIds'] = $request->input('productIds'); //[ 1, 2, 4, 5 ,6]
            // $data['productIds'] = "all";

            if ($data['productIds'] != 'all') {
                $data['productIds'] = explode(',', $data['productIds']);
            }

            $request->session()->put('bulk_edit_data', $data);
        } else {
            // Retrieve data from session on GET request
            $data = $request->session()->get('bulk_edit_data', [
                'target_page' => $data['target_page'] ?? 1,
                'version_id' => $data['version_id'] ?? 1,
                'productIds' => $data['productIds'] ?? []
            ]);
        }

        $bulk_edit = new BulkEdit();
        $sidebar_display = false;
        $bulk_edit = $bulk_edit->fetch_bulk_edit_apimio_products($data);

        return Inertia::render('BulkEdit', [
            'bulk_edit' => $bulk_edit,
            'sidebar_display' => $sidebar_display,
        ]);
    }

    public function products_save(Request $request)
    {
        $data = $request->all();

//        $data = [
//            'products' => [
//
//                1 => [
//                    "Default" => [
//                        "brand" => [1],
//                        "category" => [1],
//                        "store" => [2],
//                        "variants" => [
//                            [
//                                "id" => 2,
//                                "name" => "XS",
//                                "sku" => "SUTAB1191A",
//                                "price" => "230.00",
//                                "compare_at_price" => "2500.00",
//                                "cost_price" => null,
//                                "quantity" => "2737",
//                                "barcode" => "152347856",
//                                "weight" => "90",
//                                "weight_unit" => "g",
//                                "inventories" => [
//                                    [
//                                        "id" => 524,
//                                        "available_quantity" => 3,
//                                    ],
//                                    [
//                                        "id" => 526,
//                                        "available_quantity" => 0,
//                                    ]
//                                ]
//                            ],
//                            [
//                                "id" => 3,
//                                "name" => "S",
//                                "sku" => "SUTAB1191B",
//                                "price" => "2300.00",
//                                "compare_at_price" => null,
//                                "cost_price" => null,
//                                "quantity" => "7500",
//                                "barcode" => "",
//                                "weight" => "90",
//                                "weight_unit" => "g",
//                            ],
//                        ]
//                    ],
//                    "General" => [
//                        1 => [
//                            [
//                                "value" => "Paradise 21-404-updated",
//                                "unit" => null
//                            ]
//                        ],
//                        8 => [
//                            [
//                                "value" => "<p>updated SFSD FA&nbsp; SDAGD FSA FSDF DDAF SDA FDA G</p>",
//                                "unit" => null
//                            ]
//                        ],
//                        3 => [
//                            [
//                                "value" => "800",
//                                "unit" => null
//                            ]
//                        ],
//
//                    ],
//                    'custom' => [
//                        13 => [
//                            [
//                                "value" => "A",
//                                "unit" => null,
//                            ]
//                        ],
//                        14 => [
//                            [
//                                "value" => "red",
//                                "unit" => null,
//                            ]
//
//                        ]
//                    ]
//
//
//                ]
//            ],
//            'data_required' => [
//                'organization_id' => 1,
//                'user' => auth()->user(),
//                'version_id' => 1,
//                'productIds' => [1, 2, 3],
//                'target_page' => 2,
//            ]
//        ];

        return (new BulkEdit())->bulk_edit_products_save($data);
    }


    public function save_filter(Request $request)
    {

        try {
            $data = $request->all();
            // $data = [
            //     'payload' => [
            //         "Default" => [
            //             "handle" => 'Product Identifier',
            //             "brand" => 'Brand',
            //         ],
            //         "General" => [
            //             "product_name" => 'Product Name',
            //             "description" => 'Description',
            //         ]
            //     ]
            // ];

            $filter = Filter::where('type', 'bulk')->first();
            if (!$filter) {
                $filter = new Filter();
            }
            $data['type'] = 'bulk';
            $response = $filter->set_data($data)->store(
                function ($error) {
                    Log::error("Bulk filter Store Error for User " . auth()->user()->id);
                    Log::error($error);
                    return response()->json(['success' => false], 404);
                },
                function ($filter) {
                    return response()->json(['success' => true, 'filter_attributes' => $filter->payload], 200);
                }
            );

            return $response;
        } catch (\Exception $e) {
            Log::error("Bulk Edit Filters error for user " . auth()->user()->id);
            Log::error($e->getMessage());
            return response()->json(['success' => false], 404);
        }
    }
}
