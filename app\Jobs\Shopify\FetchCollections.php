<?php

namespace App\Jobs\Shopify;

use App\Classes\Shopify\RetrieveCollection;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchCollections implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $event)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $event = $this->event;
        //save collections and link it with product
        $retrieve_collections = new RetrieveCollection($event->channel_id, $event->organization_id,$this->batch());
        $retrieve_collections->getCollection();
        // $retrieve_collections->getCollectProduct();
    }
}
