<?php
namespace App\Traits\Shopify;

use App\Models\Channel\ShopifyChannel;
use GuzzleHttp\Client;
use Log;


trait ShopifyGQL {

    private string $accessToken;
    private Client $client;

    protected function initShopifyClient(int $channelId): void
    {
        $shopifyDetails = ShopifyChannel::query()->where('channel_id', $channelId)->first();

        if (! $shopifyDetails) {
            throw new \Exception("Shopify channel details not found for channel_id: {$channelId}");
        }
        $this->accessToken = $shopifyDetails->access_token;
        $apiVersion  = config('services.shopify.api_version');

        $this->client = new Client([
            'base_uri' => "https://{$shopifyDetails->shop}/admin/api/{$apiVersion}/graphql.json",
        ]);
    }

    /**
     * Make a GraphQL query.
     *
     * @param  string  $query
     * @param  array   $variables
     * @return array
     * @throws \Exception
     */
    protected function makeGraphQLRequest(string $query, array $variables = []): array
    {
        $requestBody = [
            'query'     => $query,
            'variables' => $variables,
        ];

        try {
            $response = $this->client->post('', [
                'headers' => [
                    'X-Shopify-Access-Token' => $this->accessToken,
                    'Content-Type'           => 'application/json',
                ],
                'json' => $requestBody,
            ]);
        } catch (\Exception $e) {
            Log::channel('shopify')->error('GraphQL request failed: ' . $e->getMessage());
            throw $e;
        }

        $responseBody = json_decode($response->getBody()->getContents(), true);

        if (! empty($responseBody['errors'])) {
            Log::channel('shopify')->error(['GraphQL errors' => $responseBody['errors']]);
            throw new \Exception('GraphQL errors: ' . json_encode($responseBody['errors']));
        }

        return $responseBody;
    }

    /**
     * Optionally, handle the throttling based on Shopify's cost info.
     *
     * @param  array  $extensions
     * @param  bool   $hasNextPage
     * @return void
     */
    protected function handleThrottling(array $extensions, bool $hasNextPage): void
    {
        if (! isset($extensions['cost'])) {
            return;
        }

        $costInfo          = $extensions['cost'];
        $requestedQueryCost = $costInfo['requestedQueryCost'] ?? 0;
        $throttleStatus     = $costInfo['throttleStatus'] ?? [];
        $currentlyAvailable = $throttleStatus['currentlyAvailable'] ?? 0;
        $restoreRate        = $throttleStatus['restoreRate'] ?? 50;

        if ($hasNextPage) {
            $neededPoints = $requestedQueryCost - $currentlyAvailable;
            if ($neededPoints > 0) {
                $sleepSeconds = (int) ceil($neededPoints / $restoreRate);
                sleep($sleepSeconds);
            }
        }
    }

}
