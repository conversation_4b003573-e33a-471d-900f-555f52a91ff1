<?php

namespace App\Classes\Plan;

use Illuminate\Support\Collection;

class PlanClass
{
    /**
     * Static variable array containing plan details.
     *
     * @var Collection
     */
    public static $plans;

    /**
     * Static variable array containing plan items details.
     *
     * @var Collection
     */
    public static $items;

    /**
     * Constructor to initialize the static variable.
     */
    public function __construct()
    {
        self::initialize();
    }

    /**
     * Initialize the static variable.
     */
    public static function initialize()
    {
        if (is_null(self::$plans)) {
            self::$plans = self::getPlans();
        }
        if (is_null(self::$items)) {
            self::$items = self::getItems();
        }
    }

    /**
     * Get the plans as a collection of Plan objects.
     *
     * @return Collection
     */
    public static function getPlans(): Collection
    {
        return collect([
            new Plan(
                env('GROW_PLAN_PRICE'),
                'price_1MBcqGJKtHOhwx4oPlvfHYNu',
                env('GROW_PRODUCT_ID'),
                "Grow Plan",
                "grow_plan",
                "media/billing/community.png",
                199.00,
                588.00,
                29.99,
                1000000000000000,
                1,
                0,
                50,
                0,
                1,
                1,
                1,
                14,
                [
                    'product' => true,
                    'product-dashboard' => true,
                    'inventory' => true,
                    'gallery' => false,
                    'notification' => true,
                    'settings' => true,
                    'invite-team' => true,
                    'import' => true,
                    'import-template-save' => false,
                    'export' => true,
                    'category' => true,
                    'attribute-set' => true,
                    'attribute' => true,
                    'variant-option' => true,
                    'brand' => true,
                    'vendor' => true,
                    'language' => false,
                    'location' => true,
                    'store' => true,
                ],
                self::getItems(),
                "bg-white-smoke",
                [
                    "1000 Free SKUs",
                    "50Gb Storage",
                    "Product Data Validations and Insights",
                    "CSV Import with Formulas",
                    "Bulk Edit Basic (Product, SEO, Attributes, Variants)",
                    "Bulk Edit Pricing (Pricing, Discounts)",
                    "1 Shopify Stores Connector",
                    "3 User Management",
                    "Standard Customer Support",
                ],
                [
                    "Additional SKU : Bundles",
                ],
                1,
                "",
            ),
            new Plan(
                env('EXPAND_PLAN_PRICE'),
                env('EXPAND_PRODUCT_ID'),
                'prod_MqYgmHkWY4Vdko',
                "Expand Plan",
                "expand_plan",
                "media/billing/starter.png",
                399.00,
                4788.00,
                29.99,
                1000000000000000,
                100,
                100,
                1024,
                1,
                1,
                1,
                1,
                14,
                [
                    'product' => true,
                    'product-dashboard' => true,
                    'inventory' => true,
                    'gallery' => true,
                    'notification' => true,
                    'settings' => true,
                    'invite-team' => true,
                    'import' => true,
                    'import-template-save' => true,
                    'export' => true,
                    'category' => true,
                    'attribute-set' => true,
                    'attribute' => true,
                    'variant-option' => true,
                    'brand' => true,
                    'vendor' => true,
                    'language' => true,
                    'location' => true,
                    'store' => true,
                ],
                self::getItems(),
                "bg-warning",
                [
                    "1000 Free SKUs",
                    "1Tb Storage",
                    "Unlimited User Management",
                    "Multi-Store Support (Bundles)",
                    "Internationalization (Languages, Currency)",
                    "Campaign Management - Scheduling (Pricing, Discounts etc)",
                    "Inventory Management",
                    "CSV Templates Import",
                    "CSV Templates Export",
                    "DAM",
                    "Premium Customer Support",
                ],
                [
                    "Additional SKU : Bundles",
                    "Store Connectors : Per Store",
                    "Brand Portal : $199",
                ],
                10000,
                "",
            ),
           new Plan(
                0,
                0,
                0,
                "Free Plan",
                "standard_plan",
                "media/billing/starter.png",
                0.00,
                0.00,
                0.00,
                1000000000000000,
                100,
                100,
                1024,
                1,
                1,
                1,
                1,
                14,
                [
                    'product' => true,
                    'product-dashboard' => true,
                    'inventory' => true,
                    'gallery' => true,
                    'notification' => true,
                    'settings' => true,
                    'invite-team' => true,
                    'import' => true,
                    'import-template-save' => true,
                    'export' => true,
                    'category' => true,
                    'attribute-set' => true,
                    'attribute' => true,
                    'variant-option' => true,
                    'brand' => true,
                    'vendor' => true,
                    'language' => true,
                    'location' => true,
                    'store' => true,
                ],
                self::getItems(),
                "bg-warning",
                [
                    "1000 Free SKUs",
                    "1Tb Storage",
                    "Unlimited User Management",
                    "Multi-Store Support (Bundles)",
                    "Internationalization (Languages, Currency)",
                    "Campaign Management - Scheduling (Pricing, Discounts etc)",
                    "Inventory Management",
                    "CSV Templates Import",
                    "CSV Templates Export",
                    "DAM",
                    "Premium Customer Support",
                ],
                [
                    "Additional SKU : Bundles",
                    "Store Connectors : Per Store",
                    "Brand Portal : $199",
                ],
                10000,
                "",
            ),
        ]);
    }

    /**
     * Get the items for each plan as a collection of PlanItem objects.
     *
     * @return Collection
     */
    public static function getItems(): Collection
    {
        return collect([
            new PlanItem(
                env('SKU_PRICE'),
                env('SKU_PRODUCT_ID'),
                'Sku',
                'sku',
                "0.01"
            ),
            new PlanItem(
                env('CHANNEL_PRICE'),
                env('CHANNEL_PRODUCT_ID'),
                'Channel',
                'channel',
                "50"
            ),
            new PlanItem(
                env('BRAND_PRICE'),
                env('BRAND_PRODUCT_ID'),
                'Brand',
                'brand',
                "100"
            ),
        ]);
    }
}
