<?php

namespace App\Classes\Billing;

use Stripe\Coupon;
use App\Models\BrandsPortal;
use Illuminate\Http\Request;
use App\Models\Product\Brand;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Auth;
use App\Notifications\SlackNotification;
use Illuminate\Support\Facades\Notification;
use Stripe\Exception\InvalidRequestException;
use Laravel\Cashier\Exceptions\IncompletePayment;

class SubscriptionClass extends BillingBaseClass
{
    public function subscription($error, $success)
    {
        try {

            if (!$this->organization) {
                return $error(['main' => 'Add Organization First.']);
            }

            // check if plan exist
            if (!$this->plan) {
                return $error(['main' => 'This plan does not exist.']);
            }

            // Check if the organization has a payment method defined
            // if (!$this->organization->hasPaymentMethod()) {
            //     return $error('There is no card attached with your subscriptions');
            // }

            // Check if the plan is already subscribed
            if ($this->organization->subscribed("default", $this->stripe_price)) {
                // Check if subscribed to any of the plan's products
                if ($this->organization->subscribedToProduct($this->stripe_product_id, "default")) {
                    return $error(['main' => 'Already subscribed to this plan']);
                }
            }

            try {
                // check if subscription exists
                if ($this->current_stripe_price && $this->organization->subscribed("default", $this->current_stripe_price)) {
                    // Retrieve the current subscription
                    $subscription = $this->organization->subscription("default");

                    // Check for incomplete payment
                    if ($subscription->hasIncompletePayment()) {
                        return redirect()->route(
                            'cashier.payment',
                            [$subscription->latestPayment()->id, 'redirect' => route('dashboard')]
                        )->with('error', 'You have an incomplete payment. Please complete the payment to proceed.');
                    }

                    // Prepare the new subscription items
                    $items = [];
                    $items[$this->stripe_price] = ["quantity" => 1];
                    foreach ($this->plan->items as $item) {
                        $quantity = $this->getQuantity($item->handle);
                        if($quantity > 0){
                            $items[$item->stripe_price_id] = ["quantity" => $quantity];
                        }
                    }

                    // Apply the coupon if available
                    if (isset($this->data['coupon']) ? !empty($this->data['coupon']) : false) {
                        \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

                        try {
                            // Validate the coupon
                            $coupon = Coupon::retrieve($this->data['coupon']);

                            // Apply the coupon to the subscription
                            $subscription->updateStripeSubscription(['coupon' => $coupon->id]);
                        } catch (InvalidRequestException $e) {
                            return $error(['main' => "Your coupon is invalid."]);
                        }
                    }

                    // Swap the subscription to the new plan
                    $subscription->swap($items);

                    // Send a Slack notification about the plan change
                    $this->sendSlackNotification('Plan Upgraded', 'Plan has been Upgraded. :tada: :money_mouth_face::money_mouth_face:', 'Plan Upgraded');

                    return $success(["msg" => 'Plan changed successfully']);
                } else {

                    try {
                        // adding one so that quantity is maintained
                        $subscription = $this->organization->newSubscription("default", $this->stripe_price);

                        // Prepare the subscription items
                        $items = [];
                        foreach ($this->plan->items as $item) {

                            $quantity = $this->getQuantity($item->handle);
                            if($quantity > 0){
                                $items[] = [
                                    'price' => $item->stripe_price_id,
                                    'quantity' => $quantity,
                                ];
                            }
                        }

                        if (isset($this->data['coupon']) ? !empty($this->data['coupon']) : false) {
                            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
                            try {
                                Coupon::retrieve($this->data['coupon']);
                            } catch (InvalidRequestException $e) {
                                return $error(['main' => "Your coupon is invalid."]);
                            }
                            $subscription = $subscription->withCoupon($this->data['coupon']);
                        }

                       return $success(["msg" => "redirect to stripe client portal", "url" => $subscription->checkout([
                            'success_url' => URL::route('dashboard.success'),
                            'cancel_url' => URL::route('dashboard'),
                            'line_items' => $items,
                            'allow_promotion_codes' => true,
                        ])]);
                    } catch (IncompletePayment $exception) {
                        return redirect()->route(
                            'cashier.payment',
                            [$exception->payment->id, 'redirect' => route('dashboard')]
                        );
                    }

                    if ($subscription->stripe_status != 'active') {
                        info("Stripe Subscription:");
                        info("Stripe Subscription status is not active");
                        return $error(['main' =>  "There might be some problem upgrading your plan, Please try again."]);
                    }

                    $this->sendSlackNotification('Plan Purchased', 'Plan has been Purchased. :tada: :+1:', 'Plan Upgraded');

                    return $success(["msg" => 'Plan changed successfully']);
                }
            } catch (\Exception $exception) {
                return $error(['main' =>  "There might be some problem processing your payment, Please try again."]);
            }
        } catch (\Exception $e) {
            return $error(['main' =>  "There might be some problem processing your payment, Please try again."]);
        }
    }

    private function getQuantity($handle)
    {
        switch ($handle) {
            case 'sku':
                $count = Variant::whereHas("product",function($query){
                    $query->where("organization_id",$this->organization->id);
                })->count();
                $quantity = ($count > 0 ? $count : 0);
                return $quantity;
            case 'channel':
                return Channel::where("organization_id",$this->organization->id)->count() > 0 ? Channel::count() : 0;
            case 'brand':
                return BrandsPortal::where("organization_id",$this->organization->id)->count() > 0 ? Brand::count() : 0;
            default:
                return 0;
        }
    }

    public function sendSlackNotification($title, $content, $fields)
    {
        if (App::environment() == 'production') {
            try {
                $fields = [
                    'Name' => $this->data['user']->fname . ' ' . $this->data['user']->lname,
                    'Email' => $this->data['user']->email,
                    'Phone' => $this->data['user']->phone ?? '',
                    'Plan' => $this->plan->name,
                ];

                $channel = App::environment() != 'production' ? '#apimio-notifications-staging' : '#apimio-notifications';

                $detail = [
                    "content" => $content,
                    "fields" => $fields,
                    "title" => $title,
                    "channel" => $channel
                ];

                Notification::route('slack', env('LOG_SLACK_WEBHOOK_URL'))->notify(new SlackNotification($detail));
            } catch (\Exception $e) {
                Log::error(["slack notification error in billing"]);
                Log::error([$e->getMessage()]);
            }

        }
    }

    public function coupon_retrieve(Request $request)
    {
        if ($request->has('coupon') ? !empty($request->get('coupon')) : false) {
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
            try {
                return response(Coupon::retrieve($request->input('coupon')), 200);
            } catch (InvalidRequestException $e) {
                return response($e->getMessage(), 402);
            }
        }
        return response('coupon not found', 402);
    }
}
