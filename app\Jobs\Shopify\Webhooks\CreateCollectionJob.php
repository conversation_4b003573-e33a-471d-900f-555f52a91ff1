<?php

namespace App\Jobs\Shopify\Webhooks;

use Illuminate\Bus\Queueable;
use App\Models\Product\Category;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CreateCollectionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $channel_id;

    public $organization_id;

    public $request;

    /**
     * Create a new job instance.
     */
    public function __construct($channel_id, $organization_id, $request)
    {
        $this->channel_id = $channel_id;
        $this->organization_id = $organization_id;
        $this->request = $request;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        $array['name'] = $this->request['title'];
        $array['description'] = $this->request['body_html'];
        $array['organization_id'] = $this->organization_id;
        //$collection_id = ['type'=>'shopify','collection_id'=>$this->request['id']];
        $array['response'] = ['type'=>'shopify','collection_id'=> $this->request['id'],'channel_id'=> $this->channel_id];
        //$array['response'] = json_encode($collection_id);

        $category = new Category();
        $category->set_data($array)->store(function ($error) {
            Log::channel('shopify')->info('error in saving category in db.');
        }, function () {
            Log::channel('shopify')->info('success in saving cat.');
        });
    }
}
