<?php

namespace App\Http;

use App\Http\Middleware\ActiveOrganization;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\BillingMiddleware;
use App\Http\Middleware\CheckSession;
use App\Http\Middleware\IsVerified;
use App\Http\Middleware\PrimeAndPaidUserMiddleware;
use App\Http\Middleware\UpdateHubspotEmailVerification;
use App\Http\Middleware\VerifyStripeWebhookSignature;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
//        \Illuminate\Session\Middleware\StartSession::class,
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\CheckForMaintenanceMode::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
//        \Illuminate\View\Middleware\ShareErrorsFromSession::class,

    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Inertia\Middleware::class,
        ],

        'api' => [
            'throttle:60,1',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.stripe' => \App\Http\Middleware\SubscriptionMiddleware::class,
        'subscription.access' => \App\Http\Middleware\SubscriptionAccess::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'verified.user' => \App\Http\Middleware\EnsureUserIsVerified::class,
        'isVerified' => IsVerified::class,
        'activeOrganization' => ActiveOrganization::class,
        'check_billing' => BillingMiddleware::class,
        'prime.user' => PrimeAndPaidUserMiddleware::class,
        'stripe.webhook' => VerifyStripeWebhookSignature::class,
        'admin' => AdminMiddleware::class,
        'check_encryption' => \Apimio\Gallery\Http\Middleware\CheckEncryptionMiddleware::class,
        'check_session' => CheckSession::class,
        'updateHubspotEmailVerification' => UpdateHubspotEmailVerification::class,
    ];
}
