@extends('layouts.app_new')
@section('titles','Price Schedules')
@section('content')
    <div>
        <x-products.page-title name="Price Schedules"
                               description="View and manage your price schedules"
                               links="false"
                               button="false">

        </x-products.page-title>
        <div class="row">
            <div class="col-12 col-md-6 col-xl-3">
{{--                @if(count($data["brands"]) > 0)--}}
{{--                    <x-general.search-bar placeholder="{{trans('products_brands.search_placeholder')}}"/>--}}
{{--                @endif--}}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                @if(count($tasks) > 0)
                    <table class="table">
                        <caption style="visibility: hidden"></caption>
                        <thead class="thead">
                        <tr>
                            <th scope="col">{{ __('Name') }}</th>
                            <th scope="col">{{ __('Date and Time') }}</th>
                            <th scope="col">{{ __('Timezone') }}</th>
                            <th scope="col">{{ __('Status') }}</th>
                            <th scope="col">{{ __('Reverse Schedule') }}</th>
                            <th class="text-end">{{ __('Actions') }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($tasks as $task)
                            <tr>
                                <td>{{substr($task->name,0,50)}}</td>
                                <td>{{ \Carbon\Carbon::parse($task->scheduled_at)->format('F j, Y g:i A') }}</td>
                                <td>{{ $task->timezone }}</td>
                                <td>
                                    @if($task->status == 'pending')
                                        <span class="badge bg-warning text-dark">{{ __('Pending') }}</span>
                                    @elseif($task->status == 'completed')
                                        <span class="badge bg-success">{{ __('Completed') }}</span>
                                    @elseif($task->status == 'failed')
                                        <span class="badge bg-danger">{{ __('Failed') }}</span>
                                    @else
                                        <span class="badge bg-info">{{ __('In Progress') }}</span>
                                    @endif
                                </td>
                                <!-- Reverse Schedule -->
                                <td>
                                    <div class="my-2">
                                        @if($task->reverse && !empty($task->reverse))
                                            <!-- Display reverse schedule if exists -->
                                            @if(isset($task->reverse['scheduled_at']))
                                                <strong>{{ __('Reversed on:') }}</strong>
                                                {{ \Carbon\Carbon::parse($task->reverse_scheduled_at)->format('F j, Y g:i A') }}
                                                <br>
                                            @endif
                                            @if(isset($task->reverse['timezone']))
                                                <strong>{{ __('Reverse Timezone:') }}</strong>
                                                {{ $task->reverse['timezone'] ?? 'N/A' }}
                                                <br>
                                            @endif

                                            @if(isset($task->reverse['status']))
                                                <strong>{{ __('Reverse Status:') }}</strong>
                                                @if($task->reverse['status'] == 'pending')
                                                    <span class="badge bg-warning text-dark">{{ __('Pending') }}</span>
                                                @elseif($task->reverse['status'] == 'completed')
                                                    <span class="badge bg-success">{{ __('Completed') }}</span>
                                                @elseif($task->reverse['status'] == 'failed')
                                                    <span class="badge bg-danger">{{ __('Failed') }}</span>
                                                @else
                                                    <span class="badge bg-info">{{ __('In Progress') }}</span>
                                                @endif
                                            @endif
                                        @else
                                            <!-- If no reverse schedule, show a default message -->
                                            {{ __('No reverse schedule set') }}
                                        @endif
                                    </div>
                                </td>
                                <td class="text-end">
                                    @if($task->status != 'completed' && $task->status != 'failed')
                                        <a href="javascript:void(0)"
                                           class="mr-3 edit-btn text-decoration-none edit-bulk-price-update" data-action="price-update" data-id="{{$task->id}}">
                                            <i class="fa-regular fa-pen-to-square fs-20"></i>
                                        </a>
                                    @endif

                                    <a href="#" data-id="{{$task->id}}" data-retailer-name=""
                                       data-bs-toggle="modal" data-bs-target="#delete-modal-{{$task->id}}" class="btn-delete text-decoration-none">
                                        <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                    </a>
                                </td>
                                <x-assets.delete-modal id="{{$task->id}}" text="Are you sure you want to delete this scheduler?" button="Delete Scheduler" title="Delete Scheduler" url="{{route('tasks.destroy',$task->id)}}" type="task"/>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                    {!! $tasks->links() !!}
                @else
                    <x-general.empty-page description="No Schedulers found"/>
                @endif
            </div>

        </div>
    </div>



@endsection
@push('footer_scripts')
    <script>
        $(document).ready(function () {
            $(".edit-bulk-price-update").click(function(event) {
                self = $(this);
                event.preventDefault();
                var prev_text = self.html();
                $.ajax({
                    url: "{{ route('bulk.price.update.modal.generate') }}",
                    method: "POST",
                    data: {
                        task_id: self.data('id'),
                    },
                    async: false,
                    beforeSend: function() {

                    },
                    success: function(data) {
                        $('#bulk-price-update-modal-div').empty();
                        $('#bulk-price-update-modal-div').append(data.output);
                        $('#bulkPriceUpdate').modal('show');
                    },
                    error: function(error) {
                        console.log(error);
                    },
                    complete: function() {
                        self.html(prev_text);
                    }
                });
            });
        });
    </script>
@endpush
