<?php

namespace App\Http\Controllers\Api;

use App;
use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AuthController extends Controller
{
    public function issueToken()
    {
        // Check if the user is authenticated
        if (Auth::check()) {
            $user = Auth::user();
            // Create a token for the authenticated user
            $token = $user->createToken('API Token')->plainTextToken;

            return response()->json(['token' => $token], 200);
        }

        return response()->json(['error' => 'Unauthorized'], 401);
    }

    /**
     * Display the login form.
     *
     * @return \Illuminate\View\View
     */
    public function loginForm()
    {
        return Inertia::render('auth/Login', [
            'title' => 'Login'
        ]);
    }

    /**
     * Display the registration form.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Foundation\Application|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|\Inertia\Response
     */
    public function registerForm(){
        if (App::environment('local')) {
            return Inertia::render('auth/Register', [
                'title' => 'Register'
            ]);
        } else {
            return redirect('https://apimio.com/demo/');
        }

    }

    public function login(Request $request)
    {
        $credentials = $request->only('email', 'password');
        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            $organizations = $user->organizations->all();

            $tokenObj = $user->createToken('API Token');
            $token = $tokenObj->plainTextToken;

            return response()->json(['token' => $token, 'organizations' => $organizations], 200);
        }

        return response()->json(['error' => 'Unauthorized'], 401);
    }

    public function loginOrganization(Request $request)
    {
        if (Auth::check()) {
            $user = Auth::user();
            $organization = $user->organizations->find($request->organization_id) ?? $request->user()->organizations()->first();
            $token = $organization->createToken('API token')->plainTextToken;
            return response()->json(['token' => $token], 200);
        }

        return response()->json(['error' => 'Unauthorized'], 401);
    }
}
