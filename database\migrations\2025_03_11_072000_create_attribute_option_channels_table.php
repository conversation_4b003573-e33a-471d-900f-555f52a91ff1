<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attribute_option_channels', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('attribute_option_id');
            $table->unsignedBigInteger('channel_id');
            $table->unsignedBigInteger('product_id')->nullable();
            $table->string('store_connect_type');
            $table->string('store_connect_id');
            $table->timestamps();
            $table->foreign('attribute_option_id')->references('id')->on('attribute_options')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->foreign('channel_id')->references('id')->on('channels')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attribute_option_channels');
    }
};
