<?php

namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class ShowProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize()
    {
        // if (Gate::denies('SubscriptionAccess', 'product') || Gate::denies('create-product', \App\Models\Product\Variant::class)) {
        //     return false;
        // }
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            //
        ];
    }

    /**
    * Get the error messages for authorization failure.
    */
   protected function failedAuthorization()
   {
       abort(403, "Upgrade your plan to access all modules.");
   }
}
