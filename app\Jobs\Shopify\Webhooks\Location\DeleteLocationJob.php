<?php

namespace App\Jobs\Shopify\Webhooks\Location;

use Illuminate\Bus\Queueable;
use App\Models\Location\Location;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class DeleteLocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $channel;

    public $request;

    /**
     * Create a new job instance.
     */
    public function __construct($channel, $request)
    {
        $this->channel = $channel;
        $this->request = $request;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        if(!$this->channel){
            Log::channel('webhook')->error(['message' => 'Apimio channel not found']);
        }
        $location = Location::query()
        ->where(['organization_id' => $this->channel->organization_id])
        ->whereHas("channelLocation", function($query){
            $query->where(["channel_id" => $this->channel->id , "store_connect_id" => $this->request['id'] ]);
        })
        ->first();
        if(!$location){
            Log::channel('webhook')->error(['message' => 'Apimio location not found']);
        }
        $location->delete();
        Log::channel('webhook')->info("Location has been deleted");
    }
}
