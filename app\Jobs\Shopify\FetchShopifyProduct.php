<?php

namespace App\Jobs\Shopify;

use App\Traits\AWSSetting;
use Illuminate\Bus\Batchable;

use Illuminate\Bus\Queueable;
use App\Traits\Shopify\ShopifyAPI;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Channel\ShopifyChannel;
use Illuminate\Queue\SerializesModels;
use App\Classes\ConvertToApimioProduct;
use App\Classes\Shopify\ConvertPayload;
use Illuminate\Support\Facades\Storage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class FetchShopifyProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, AWSSetting;
    use ShopifyAPI;
    /**
     * Create a new job instance.
     *
     * @return void
     */
  //  public function __construct(public array $products, public $event)
    public function __construct(public $s3Url, public $event )
    {
        //set base url
        $shopify_channel = ShopifyChannel::query()->where('channel_id', $this->event->channel_id)->first();
        $this->setupCredentialsManually($shopify_channel->access_token, $shopify_channel->shop)->createBaseUrl();
    }

    /**
     * Execute the job.
     *
     */
    public function handle()
    {
        // 1) Get products from S3 or local:
        //    (Already done in your existing code)
        if (env('CUSTOM_QUEUE_WORKER') == 'local') {
            $products = $this->s3Url;
        } else {
            $products = $this->decodeS3Json($this->s3Url);
        }
           Log::info('Products fetched from S3');
        Log::info([$products]);
        foreach ($products as $product) {
            (new \App\Classes\Shopify\StoreProductToApimioByGQL($product['node'], $event->channel_id, $event->organization_id))
                ->store(function($obj){
                return $obj;
            });
        }
    }
}
