<?php

namespace App\Listeners\Product;

use App\Events\Product\ManageVariants;
use App\Exceptions\VariantException;
use App\Models\Product\Attribute;
use App\Models\Product\Product;
use App\Models\Notification\ErrorLog;
use App\Models\Product\Variant;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Exception;

class ManageVariantsListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param ManageVariants $event
     * @return void
     * @throws Exception
     * to create default variant, send the attribute array empty
     * example:
     * product object
     * Variants array
     * in variants array, attributes and data array is required
     * if you want to create new attributes, send the attribute without id
     *
     */
    public function handle(ManageVariants $event)
    {
        $variants = $event->variants;
        $product = $event->product;
        $version_id = $event->data['versions'][0]['id'] ?? null;
       // dd(empty($event->product));

        $error = new ErrorLog();
        if(!$version_id){
            Log::Channel('events')->info('Version id is required in ManageVariantsListener in product id '.$product?->id);
            throw new Exception('Version id is required');
        }

        //for checking sku duplication in product variants if product object is present
         if(!empty($product->variants) && !empty($variants['data'])){
             $sku = array_column($variants['data'], 'sku');
             $sku = array_filter($sku);

             $existingVariants = Variant::where('version_id', $version_id)
                 ->whereNot('product_id', $product->id)
                 ->whereRelation('product', 'organization_id', $product->organization_id)
                 ->whereIn('sku', $sku)
                 ->first();

             if ($existingVariants) {
                 $variant_options = json_decode($existingVariants->option)->options ?? [];
                 throw new VariantException('Variant already exist in product identifier ( ' . $product->sku . ' ) with variant sku ( ' . $existingVariants->sku . " ) & variant option ( " . implode(",", $variant_options) . " )");
             }
         }

        if(!empty($variants)) {
            if(!empty($product)) {
                //check if in any of $variants['data'] array options key not exists then throw exception
                if (empty($variants['data'])) {
                    Log::channel('events')->info('Data is required in ManageVariantsListener in product id '.$product->id);
                    throw new VariantException('Data is required in variants data', 500, null);
                }

                //check if any of $variants['data'] options is empty then throw exception
                $options = array_column($variants['data'], 'options');
                if (count($options) !== count($variants['data']) && count($variants['data']) > 1) {
                    throw new VariantException('Variant Options not found for product identifier ( '.$product->sku.' )', 500, null);
                }

                //check if variant sku is duplicate or not in $variants['data']
                $sku = array_column($variants['data'], 'sku');
                $sku = array_filter($sku);

                if (count($sku) != count(array_unique($sku))) {
                    Log::channel('events')->info('Duplicate sku found in product id ' . $product->id);
                    $data = [
                        'organization_id' => $product->organization_id,
                        'description' => "Duplicate sku found in product id '.$product->id </b>.",
                        'type' => 'shopify',
                        'link' => route('products.edit', $product->id),
                        'link_text' => 'View Product',
                        'status' => 'error',
                    ];
                    (new ErrorLog())->saveErrorLog($data);
                    throw new VariantException('Duplicate sku found in variants data with product id ' . $product->id, 500, null);
                }


                if (empty($variants['attributes']) && count($variants['data']) == 1) {
                    $variants['attributes'] = Attribute::query()
                        ->where([
                            'organization_id' => $product->organization_id,
                            'is_default' => 1,
                            'attribute_type_id' => 13
                        ])
                        ->select('id')
                        ->get()->toArray();

                    $singleAttribute = Attribute::query()
                        ->where([
                            'organization_id' => $product->organization_id,
                            'is_default' => 1,
                            'attribute_type_id' => 13
                        ])->with('attribute_options')
                        ->first();

                    $event->data['options'] = $singleAttribute->attribute_options->pluck('name')->toArray();
                }
                $variants['attributes'] = $this->create_or_update_variant_attributes($variants['attributes'], $product, $event);
                $product->saveVariantsWithVersion($variants,$event->data);
            }else{
                (new Product())->saveVariantsWithVersion($variants, $event->data);
            }
        }
    }


    /**
     * @param $variant_attributes
     * @param $product
     * @return array
     */
    function create_or_update_variant_attributes($variant_attributes, $product, $event): array
    {
        $attributes =  array_map(function ($attribute) use($product ,$event) {

            if(isset($attribute['id']) && !isset($attribute['name']) && !isset($attribute['options'])){
               return $attribute;
            }
            if(!isset($attribute['name']) || !isset($attribute['options'])){
                Log::channel('events')->info('Attribute name or options is required in ManageVariantsListener in product id '.$product->id);
                throw new VariantException('Attribute name or options is required');
            }
            $attribute_obj = new Attribute();
            $attribute["organization_id"] = $product->organization_id;
            $attribute["attribute_options"] = array_map(
                    function ($row) {
                        return ["name" => $row];
                    },
                    (
                        !empty($attribute["options"])
                        ?array_filter($attribute["options"])
                        :[]
                    )
                );
            $attribute["attribute_type_id"] = 13;
            if(isset($attribute['shopify_id'])){
                $attribute["shopify_id"] = $attribute['shopify_id'];
            }

            if(isset($event->data['channel_id'])) {
                $attribute["channel_id"] = $event->data['channel_id'];
            }
            $attribute['method_type'] = $event->data['template_method_type'] ?? null;
            if(!isset($attribute['id'])){
                $attribute['id'] = Attribute::query()->where([
                            'organization_id'=> $product->organization_id,
                            'attribute_type_id' => 13,
                            'name' => $attribute['name']
                        ])
                        ->first()?->id;
            }
            $attribute_obj->set_data($attribute)->store(
            // error function
                function ($errors) {
                    Log::channel('events')->info('error in creating attribute');
                    Log::channel('events')->error($errors);
                },
                // success function
                function ($obj) use (&$attribute) {
                    $attribute["id"] = $obj->id;
                }
            );
            return [
                "id" => $attribute["id"] ?? null,
            ];

        }, $variant_attributes);
       // dump($attributes);
        return $attributes;
    }
}
