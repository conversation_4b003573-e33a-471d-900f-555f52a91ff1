<?php

namespace App\Classes\Shopify;

use App\Traits\Shopify\ShopifyGQL;

class FetchDataByGQL
{
    use ShopifyGQL;

    public function __construct(int $channelId)
    {
        $this->initShopifyClient($channelId);
    }

    /**
     * Fetch products from Shopify using GraphQL with pagination.
     *
     * @param  callable  $callback  A callback to process each batch of products (edges)
     *
     * @throws \Exception
     */
    public function fetchProducts(callable $callback)
    {
        $query = <<<'GQL'
            query getProducts($first: Int!, $after: String) {
              products(first: $first, after: $after) {
                edges {
                  cursor
                  node {
                    id
                    title
                    handle
                    descriptionHtml
                    productType
                    vendor
                    tags
                    status
                    totalInventory
                    options {
                      id
                      name
                      values
                    }
                    seo {
                      title
                      description
                    }
                    collections(first:250) {
                        edges {
                            node {
                              id
                              handle
                            }
                        }
                    }
                    media(first: 250) {
                      edges {
                        node {
                          id
                          ... on MediaImage {
                            createdAt
                            image {
                              width
                              height
                              url
                            }
                          }
                        }
                      }
                    }
                    variants(first: 100) {
                      edges {
                        node {
                          id
                          title
                          sku
                          price
                          compareAtPrice
                          position
                          inventoryPolicy
                          barcode
                          selectedOptions {
                            name
                            value
                          }
                          inventoryQuantity
                          inventoryPolicy
                          inventoryItem {
                            id
                            tracked
                          }
                          image {
                            id
                          }
                        }
                      }
                    }
                  }
                }
                pageInfo {
                  hasPreviousPage
                  hasNextPage
                  startCursor
                  endCursor
                }
              }
            }
            GQL;

        $afterCursor    = null;
        $productsPerPage = 2;

        do {
            $variables = [
                'first' => $productsPerPage,
                'after' => $afterCursor,
            ];

            $response = $this->makeGraphQLRequest($query, $variables);

            // Check that products data exists.
            $productsData = $response['data']['products'] ?? null;
            if (! $productsData) {
                throw new \Exception('No products data found in Shopify response.');
            }

            $edges    = $productsData['edges'] ?? [];
            $pageInfo = $productsData['pageInfo'] ?? [];
            $hasNextPage = $pageInfo['hasNextPage'] ?? false;
            $afterCursor = $pageInfo['endCursor'] ?? null;

            $callback($edges);

            // Handle throttling if needed based on Shopify's response.
            if (isset($response['extensions'])) {
                $this->handleThrottling($response['extensions'], $hasNextPage);
            }
        } while ($hasNextPage);
    }

    public function metaFieldDefinition(int $first = 250, string $ownerType = 'PRODUCT'): array
    {
        // Define the GraphQL query using variables for dynamic values.
        $query = <<<'GQL'
            query metafieldDefinitions($first: Int!, $ownerType: MetafieldOwnerType!) {
                metafieldDefinitions(first: $first, ownerType: $ownerType) {
                    edges {
                        node {
                           id
                            name
                            namespace
                            key
                            description
                            type {
                              name
                            }
                            validations {
                                name
                                type
                                value
                            }
                        }
                    }
                }
            }
        GQL;

        // Set the variables to be passed to the query.
        $variables = [
            'first'     => $first,
            'ownerType' => $ownerType,
        ];

        // Execute the GraphQL query with the dynamic variables.
        $response = $this->makeGraphQLRequest($query, $variables);

        // Retrieve the metafield definitions from the response.
        $metafieldDefinitions = $response['data']['metafieldDefinitions'] ?? null;

        if (!$metafieldDefinitions) {
            throw new \Exception("No metafield definitions data found in Shopify response.");
        }

        return $metafieldDefinitions;

    }

    public function locations(int $first = 250): array
    {
        // Define the GraphQL query using variables for dynamic values.
        $query = <<<'GQL'
            query  {
                locations(first: $first) {
                    edges {
                        node {
                            id
                            name
                            address {
                                address1
                                address2
                                city
                                country
                                province
                                zip
                                phone
                            }
                        }
                    }
                }
            }
        GQL;

        // Set the variables to be passed to the query.
        $variables = [
            'first' => $first,
        ];

        $response = $this->makeGraphQLRequest($query, $variables);

        $locations = $response['data']['locations'] ?? null;

        if (!$locations) {
            throw new \Exception("No locations data found in Shopify response.");
        }

        return $locations;
    }

    public function collections(int $first = 250): array
    {
        // Define the GraphQL query using variables for dynamic values.
        $query = <<<'GQL'
            query CustomCollectionList($first: Int!, $query: String!) {
                  collections(first: $first, query: $query) {
                    nodes {
                      id
                      handle
                      title
                      descriptionHtml
                    }
                  }
                }
        GQL;

        // Set the variables to be passed to the query.
        $variables = [
            'first' => $first,
            'query' => 'collection_type:custom',
        ];

        $response = $this->makeGraphQLRequest($query, $variables);

        $collections = $response['data']['collections'] ?? null;

        if (!$collections) {
            throw new \Exception("No collections data found in Shopify response.");
        }

        return $collections;
    }



}

