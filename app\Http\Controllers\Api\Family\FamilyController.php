<?php

namespace App\Http\Controllers\Api\Family;

use App\Http\Controllers\Controller;
use App\Http\Requests\Family\FamilyRequest;
use App\Http\Resources\FamilyResource;
use App\Models\Product\Family;
use Illuminate\Http\Request;

class FamilyController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }


            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:families,id',
                'name' => 'string|max:255',
                'is_default' => 'boolean',
                'paginate' => 'integer|min:1|max:255',
            ]);

            // Query to get families without global scopes
            $families = $user->families()->with('attributes');

            // Apply filters if provided
            if ($request->filled("id")) {
                $families->where("id", $request->get("id"));
            }

            if ($request->filled("name")) {
                $families->where("name", "LIKE", "%" . $request->get("name") . "%");
            }

            if ($request->filled("is_default")) {
                $families->where("is_default", $request->get("is_default"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get the paginated data
            $paginatedFamilies = $families->orderBy('updated_at', 'desc')->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedFamilies->currentPage(),
                'last_page' => $paginatedFamilies->lastPage(),
                'per_page' => $paginatedFamilies->perPage(),
                'total' => $paginatedFamilies->total(),
            ];

            return $this->successResponse(
                'Families retrieved successfully',
                FamilyResource::collection($paginatedFamilies),
                200,
                $pagination
            );

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve families', $e->getCode() != 0 ? $e->getCode() : 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FamilyRequest $request)
    {
        $family = $request->user()->families()->create($request->validated());

        if (isset($data["attribute_ids"])) {
            $family->attributes()->sync($data["attribute_ids"]);
        }

        return response([
            'message' => 'Family created successfully',
            'family' => new FamilyResource($family)
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $family = $request->user()->families()->with('attributes')->findOrFail($id);
        return response([
            'message' => 'Family retrieved successfully',
            'family' => new FamilyResource($family)
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FamilyRequest $request, string $id)
    {
       $family = $request->user()->families()->findOrFail($id);
        $family->update($request->validated());

        if (isset($data["attribute_ids"])) {
            $family->attributes()->sync($data["attribute_ids"]);
        }
        return response([
            'message' => 'Family updated successfully',
            'family' =>  new FamilyResource($family)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id)
    {
        // Delete the family if it is not the default
        $request->user()->families()->where('id', $id)->where('is_default', 0)->firstOrFail()->delete();

        return response([
            'message' => 'Family deleted successfully'
        ]);
    }
}
