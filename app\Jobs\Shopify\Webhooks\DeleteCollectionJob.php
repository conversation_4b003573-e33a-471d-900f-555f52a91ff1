<?php

namespace App\Jobs\Shopify\Webhooks;

use Illuminate\Bus\Queueable;
use App\Models\Product\Category;
use Illuminate\Queue\SerializesModels;
use App\Models\Channel\CategoryChannel;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class DeleteCollectionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $channel_id;

    public $organization_id;

    public $request;

    /**
     * Create a new job instance.
     */
    public function __construct($channel_id, $organization_id, $request)
    {
        $this->channel_id = $channel_id;
        $this->organization_id = $organization_id;
        $this->request = $request;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        $category = Category::query()
            ->where('organization_id',$this->organization_id)
            ->whereHas('channel',function($q){
                $q->where('store_connect_type','shopify')
                    ->where('store_connect_id',$this->request['id'])
                    ->where('channel_id',$this->channel_id);
            })
            ->first();
        CategoryChannel::query()->where([
            'channel_id'=>$this->channel_id,
            'category_id'=>$category->id,
            'store_connect_type'=>'shopify',
            'store_connect_id'=>$this->request['id']
        ])->delete();
    }
}
