<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('tasks', function (Blueprint $table) {
            // Add a foreign key to the organizations table
            $table->foreignId('organization_id')->after('id')
                ->constrained('organizations')
                ->onDelete('cascade');

            $table->string('name')->nullable()->after('organization_id');

            $table->json('json_payload')->nullable()->after('name');

            $table->string('type')->after('json_payload');

            // Add scheduled_at column to store the execution time in UTC
            $table->timestamp('scheduled_at')->nullable()->after('type');

            // Add timezone column to store user's timezone for reference
            $table->string('timezone')->nullable()->after('scheduled_at');

            $table->enum('status', ['pending', 'processing', 'completed'])->default('pending')->after('timezone');

            // Adding a JSON column to store reversal details
            $table->json('reverse')->nullable()->after('status');

            $table->softDeletes(); // Adds 'deleted_at' column
        });
    }

    public function down(): void
    {
        Schema::table('tasks', function (Blueprint $table) {
            // Drop added columns
            $table->dropForeign(['organization_id']);
            $table->dropColumn(['organization_id', 'json_payload', 'type', 'scheduled_at', 'timezone', 'status', 'name', 'reverse']);
            $table->dropSoftDeletes(); // Removes 'deleted_at' column
        });
    }
};
