<?php

namespace App\Classes\Shopify;

use App\Models\Channel\CategoryChannel;
use App\Models\Product\Category;
use App\Models\Product\CategoryProduct;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SyncCollection
{

    public array $main_category = [];

    public function __construct(
        public $category,
        public $shopify_product_id,
        public $shopify_base_url,
        public $organization_id,
        public $channel_id
    ){
    }

    public function createCollectionArray()
    {
        $category_list = $this->category ;
        $shopify_product_id = $this->shopify_product_id;

        $new_category = [];
        $categories = [];
        foreach($category_list as $category ){

            $first_category = true;
            do {
                if($first_category){
                    $category = $category;
                    $first_category = false;
                }
                else{
                    $category = Category::query()
                        ->where('id',$category->category_id)
                        ->where('organization_id',$this->organization_id)
                        ->with('channel',function($q){
                            $q->where('channel_id',$this->channel_id)
                                ->where('store_connect_type','shopify');
                        })
                        ->first();

                }
                if($category){
                    if($category->channel)
                    {
                        $categories[] =  [
                            'product_id' => $shopify_product_id,
                            'collection_id'=> $category->channel->store_connect_id

                        ];

                    }
                    else{
                        $new_category[] = [
                            'id'       =>$category->id,
                            'title'    =>$category->name,
                            'descriptionHtml'=>$category->description,
                            // 'collects' =>[
                            //     ['product_id'=>$shopify_product_id]
                            // ],
                        ];
                    }

                }

            }while($category->category_id);

        }


        $this->main_category = [
            'new_categories' => $new_category,
            'collects' => $categories,
        ];

        return $this;
    }

    public function sync(): void
    {
        // if(!empty($this->main_category['collects'])){
            $this->syncCollects($this->main_category['collects']);
        // }

        if(!empty($this->main_category['new_categories'])){
            $this->syncCollections($this->main_category['new_categories']);
        }
    }

    public function syncCollections($collections): void
    {
        foreach ($collections as $collection) {
            $cat_id = $collection['id'];
            unset($collection['id']);
            usleep(600000);
            try{
                $response = Http::retry(1, 1000)->post($this->shopify_base_url . "custom_collections.json", [
                    "custom_collection" => $collection
                ]);
                if($response->successful()){
                    $data = $response->json();
                    $cat = CategoryChannel::query()
                        ->where('category_id',$cat_id)
                        ->where('channel_id',$this->channel_id)
                        ->where('store_connect_type','shopify')
                        ->first();
                    if(!$cat){
                        $cat = new CategoryChannel();
                    }
                    $cat->category_id = $cat_id;
                    $cat->channel_id = $this->channel_id;
                    $cat->store_connect_type = 'shopify';
                    $cat->store_connect_id = $data['custom_collection']['id'];
                    $cat->save();

                }else{
                    $data = $response->json();
                    Log::channel('shopify')->info('COLLECTION SYNCING ERROR');
                    Log::channel('shopify')->error($data);
                }
            }catch(\Exception $exception){
                Log::channel('shopify')->info('Sync COLLECTS LIST TO SHOPIFY ERROR');
                Log::channel('shopify')->info($exception->getMessage());
                continue;
            }

        }
    }

    public function syncCollects($collects): void
    {

        $this->getAndDeleteProductCollects();

        foreach ($collects as $collect) {
            usleep(600000);
            try{
                $response = Http::retry(1, 1000)
                    ->post($this->shopify_base_url . "collects.json", [
                        "collect" =>$collect,
                    ]);
                if($response->successful()){
                    Log::channel('shopify')->info('Sync COLLECTS LIST TO SHOPIFY');
//                Log::channel('shopify')->info($response->json());
                }else{
                    Log::channel('shopify')->info('Sync COLLECTS LIST TO SHOPIFY ERROR');
                    Log::channel('shopify')->info($response->json());
                }

            }catch(\Exception $exception){
                Log::channel('shopify')->info('Sync COLLECTS LIST TO SHOPIFY ERROR');
                Log::channel('shopify')->info($exception->getMessage());
                continue;
            }
        }
    }

    public function getAndDeleteProductCollects(): void
    {
        usleep(600000);
        $response = Http::retry(1, 1000)
            ->get($this->shopify_base_url . "collects.json?product_id=".$this->shopify_product_id);
        if($response->successful()){
            $data = $response->json();
            foreach($data['collects'] as $collect){
                $this->deleteCollectsLinks($collect['id']);
            }
        }else{
            Log::channel('shopify')->info('GET COLLECTS LIST FROM SHOPIFY ERROR');
            Log::channel('shopify')->info($response->json());
        }
    }

    public function deleteCollectsLinks($collect_id): void
    {
        try{
            usleep(600000);
            $response = Http::retry(1, 1000)
                ->delete($this->shopify_base_url . "collects/".$collect_id.".json");

            if($response->successful()){
                Log::channel('shopify')->info('DELETE COLLECTS LIST  SHOPIFY ');
//            Log::channel('shopify')->info($response->json());
            }else{
                Log::channel('shopify')->info('DELETE COLLECTS LIST  SHOPIFY ERROR');
                Log::channel('shopify')->info($response->json());
            }
        }catch(\Exception $exception){
            Log::channel('shopify')->info('Sync COLLECTS LIST TO SHOPIFY ERROR');
            Log::channel('shopify')->info($exception->getMessage());
        }
    }
}
