<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\OrganizationRequest;
use App\Models\Organization\Organization;
use App\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Inertia\Inertia;

class OrganizationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if (get_class($request->user()) == Organization::class) {
            $organizations = $request->user()->relatedOrganizations();
        } elseif (get_class($request->user()) == User::class) {
            $organizations = $request->user()->organizations();
        }
        return response([
            'message' => 'Organizations retrieved successfully',
            'organizations' => $organizations->orderBy('updated_at', 'desc')->get()
        ]);
    }
    public function renderOrganizationSelect()
    {
        return Inertia::render('organizationselect/OrganizationSelect', [
            'title' => 'Select Organization'
        ]);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    { {
        }
        if ($request->get('id')) {
            $org = $request->user()->relatedOrganizations()->findOrFail($request->get('id'));
        } else {
            $org = $this->get()->first();
        }
        session(
            [
                'organization_id' => $org->id,
            ]
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(OrganizationRequest $request)
    {
        if (get_class($request->user()) == User::class) {
            $user = $request->user();
            // $organization = $request->user()->organizations()->create($request->validated());
        } else {
            $user = $request->user()->user;
            // Create the organization
            // $organization = $request->user()->relatedOrganizations()->create($request->validated());
        }

        // // Assign data for seeding
        // $organization->data = $request->all();

        // // Call the method to seed default account data
        // $organization->seed_default_account_data($organization);


        // $controller = new AuthController();
        // $controller->loginOrganization($request);
        $organization = new Organization();
        return $organization->set_user($user)
            ->set_data($request->all())
            ->store(
                // when error
                function ($errors) {
                    return response([
                        'message' => 'Failed to create organization',
                        'errors' => $errors
                    ], 422);
                },

                // when success
                function ($obj) {
                    response([
                        'message' => 'Organization created su        ccessfully',
                        'oragnization' => $obj
                    ]);
                }
            );
        // response([
        //     'message' => 'Organization created su        ccessfully',
        //     'oragnization' => $organization
        // ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $org = $request->user()->relatedOrganizations()->findOrFail($id);
        return response([
            'message' => 'Organization retrieved successfully',
            'oragnization' => $org
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // $org = $request->user()->relatedOrganizations()->findOrFail($id);
        // $org->update($request->all());
        // return response([
        //     'message' => 'Organization updated successfully',
        //     'data' => $org
        // ]);
        if (get_class($request->user()) == User::class) {
            $user = $request->user();
        } else {
            $user = $request->user()->user;
        }
        $organization = new Organization();
        return $organization->set_user($user)
            ->set_data($request->all())
            ->store(
                // when error
                function ($errors) {
                    return response([
                        'message' => 'Failed to update organization',
                        'errors' => $errors
                    ], 422);
                },

                // when success
                function ($obj) {
                    response([
                        'message' => 'Organization updated su        ccessfully',
                        'oragnization' => $obj
                    ]);
                }
            );
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // $org = $request->user()->organizations()->findOrFail($id);
        // $org->delete();
        // return response([
        //     'message' => 'Organization deleted successfully'
        // ]);
        return response([
            'message' => 'Organization delete not allowed'
        ]);
    }
}
