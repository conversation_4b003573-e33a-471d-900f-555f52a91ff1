
@php
    // If $task is null (new creation), defaults are provided to avoid errors
    $task = $task ?? null;

    $isProcessing        = $task ? $task->is_processing : false;
    $bulkUpdatePriceType = $task ? $task->bulk_update_price_type : '';
    $isSchedule          = $task ? $task->is_schedule : false;
    $isReverse           = $task ? $task->is_reverse : false;
    $bulkPrice           = $task ? $task->bulk_price : '';
    $salePrice           = $task ? $task->sale_price : '';
    $scheduledAt         = $task ? $task->scheduled_at : '';
    $reverseScheduledAt  = $task ? $task->reverse_scheduled_at : '';
    $filter_products     = $task ? $task->filter_products : '';
    $products            = $task ? $task->products : '';
    $assignFlag          = $task ? $task->assign_flag : null;
    $task_version        = $task ? $task->version : null;
    $timezone            = $task ? $task->timezone_value : '';
    $reverseTimezone     = $task ? $task->reverse_timezone_value : '';
    $taskId              = $task ? $task->id : '';
@endphp

<style>
    .selectize-input {
        min-height: 40px !important;
        border: 1px solid #ccc;
        padding: 6px 12px;
        display: inline-block;
        width: 100% !important;
        overflow-y: auto !important;
        position: relative;
        z-index: 1;
        box-sizing: border-box;
        box-shadow: none;
        border-radius: 4px;
    }

    fieldset.scheduler-border {
        border: 1px groove #ddd !important;
        border-radius: 4px;
        padding: 0px 15px;
        box-shadow: 0px 0px 0px 0px #000;
    }

    legend.scheduler-border {
        font-size: 1em !important;
        font-weight: bold !important;
        text-align: left !important;
        width: auto;
        padding: 0 10px;
        border-bottom: none;
        top: -10px;
        left: 5px;
        background: #fff;
        position: relative;
    }

    .custom-disabled {
        pointer-events: none;
        opacity: 0.6;
        cursor: not-allowed;
    }
</style>

<div class="modal fade" id="bulkPriceUpdate" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="staticBackdropLabel">Bulk Price Update</h2>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0 cancel-button-js" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <form action="{{ route('bulk.assign') }}" method="POST" class="formStyle" id="bulk_assign_form">
                @csrf
                <div class="modal-body">
                    <div class="row" id="section2">

                        <input type="hidden" class="bulk_products_filter_array" name="filter_products" value="{{ $filter_products }}">
                        <input type="hidden" class="bulk_productIds" name="products" value="{{ $products }}">
                        <input type="hidden" name="edit_task" id="edit_task" value="{{ $task ? 'update' : 'create' }}">
                        <input type="hidden" name="id" id="task_id" value="{{ $taskId }}">
                        <input type="hidden" name="required_data[assign_flag]" value="{{ $assignFlag ?? '1' }}">

                        <input type="hidden" id="selected_options" name="selected_options" value="price">

                        <div class="col-12">
                            <div class="languge-dropdown-bulkassign">
                                <div class="form-group mb-3">
                                    @if($task_version)
                                        <input type="hidden" name="required_data[version]" value="{{ $task_version }}">
                                    @else
                                        @if(isset($all_versions) && count($all_versions) == 1)
                                            @foreach($all_versions as $version_key => $version)
                                                <input type="hidden" class="form-control w-100 bg-white-smoke" name="required_data[version]" value="{{$version_key}}" style="pointer-events: none;">
                                            @endforeach
                                        @else
                                            <h3 class="fw-700 mb-1">In which language you want to
                                                assign price value?</h3>
                                            <select class="form-control w-100 bg-white-smoke" name="required_data[version]" required>
                                                <option value="">Select Language</option>
                                                @if(isset($all_versions))
                                                    @foreach($all_versions as $version_key => $version)
                                                        <option value="{{$version_key}}">{{$version}}</option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        @endif
                                    @endif

                                </div>
                            </div>
                            <div class="row d-flex justify-items-center align-items-center mb-2">
                                <div class="col-6">
                                    <div class="form-group d-flex justify-items-center align-items-center gap-2">
                                        <input type="radio" id="update_price"
                                               class="bulk_update_price_type {{ $isProcessing ? 'custom-disabled' : '' }}"
                                               name="required_data[bulk_update_price_type]" value="update_price"
                                            {{ $bulkUpdatePriceType === 'update_price' || empty($bulkUpdatePriceType) ? 'checked' : '' }}>
                                        <label for="update_price" class="{{ $isProcessing ? 'custom-disabled' : '' }}">Update Price</label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group d-flex justify-items-center align-items-center gap-2">
                                        <input type="radio" id="sale_price"
                                               class="bulk_update_price_type {{ $isProcessing ? 'custom-disabled' : '' }}"
                                               name="required_data[bulk_update_price_type]" value="sale_price"
                                            {{ $bulkUpdatePriceType === 'sale_price' ? 'checked' : '' }}>
                                        <label for="sale_price" class="{{ $isProcessing ? 'custom-disabled' : '' }}">Apply Sale</label>
                                    </div>
                                </div>
                            </div>

                            @php
                                $priceColClass = $bulkUpdatePriceType === 'sale_price' ? 'col-6' : 'col-12';
                                $salePriceColClass = $bulkUpdatePriceType === 'sale_price' ? 'col-6' : 'd-none';
                            @endphp

                            <div class="row">
                                <div id="price_field" class="{{ $priceColClass }}">
                                    <div class="form-group">
                                        <div>
                                            <label for="name">Price</label>
                                            <a class="clr-light ms-1" href="#" data-bs-toggle="tooltip" data-bs-html="true" data-bs-placement="top" title="
                                                <div class='text-start'>
                                                <b>Example values:</b>
                                                <li>+50  Addition</li>
                                                <li>-50  Subtraction</li>
                                                <li>+50%  add percentage</li>
                                                <li>-50%  sub percentage</li>
                                                </div>">
                                                <i class="fa fa-regular fa-circle-info"></i>
                                            </a>
                                        </div>
                                        <input type="text"
                                               class="form-control apply_bulk_price_calculate_validation_js bulk_price"
                                               placeholder="+20% etc"
                                               name="attributes[bulk_price][price]"
                                               {{ $isProcessing ? 'readonly' : '' }}
                                               value="{{ $bulkPrice }}">
                                    </div>
                                </div>

                                <div id="sale_price_field" class="{{ $salePriceColClass }}">
                                    <div class="form-group">
                                        <div>
                                            <label for="name">Sale (% value to apply)</label>
                                        </div>
                                        <div class="input-group">
                                            <input type="number"
                                                   onkeydown="if(['e', 'E', '+', '-'].includes(event.key)) event.preventDefault();"
                                                   class="form-control sale_price_input"
                                                   name="attributes[bulk_price][sale_price]"
                                                   id="bulk_sale_price"
                                                   {{ $isProcessing ? 'readonly' : '' }}
                                                   value="{{ $salePrice }}">
                                            <span class="input-group-text" style="line-height: normal">%</span>
                                        </div>
                                        <div id="sale_price_field_div_id"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3 d-flex align-items-center gap-2 mt-3">
                                <input type="checkbox"
                                       class="h-auto {{ $isProcessing ? 'custom-disabled' : '' }}"
                                       name="required_data[is_schedule]"
                                       id="is_schedule"
                                       {{ $isSchedule ? 'checked' : '' }}
                                       onclick="toggleScheduleFields()">
                                <label class="form-check-label">Would you like to schedule?</label>
                            </div>

                            <div class="row {{ $isSchedule ? '' : 'd-none' }}" id="schedule_fields">
                                <div class="form-group mb-3 col-6">
                                    <label for="scheduled_at">Date and Time</label>
                                    <input type="datetime-local"
                                           min="{{ date('Y-m-d\TH:i') }}"
                                           class="form-control scheduled_date is_schedule_required"
                                           id="scheduled_at"
                                           name="required_data[scheduled_at]"
                                           {{ $isProcessing ? 'readonly' : '' }}
                                           value="{{ $scheduledAt }}">
                                </div>
                                <div class="form-group mb-3 col-6">
                                    <label for="timezone">Timezone:</label>
                                    <select id="timezone"
                                            name="required_data[timezone]"
                                            class="form-control timezone is_schedule_required {{ $isProcessing ? 'custom-disabled' : '' }}">
                                        <option value="">Loading...</option>
                                    </select>
                                </div>

                                <div class="mb-3 d-flex align-items-center gap-2">
                                    <input type="checkbox"
                                           class="h-auto {{ $isProcessing ? 'custom-disabled' : '' }}"
                                           name="required_data[is_reverse]"
                                           id="sale_reverse"
                                           {{ $isReverse ? 'checked' : '' }}
                                           onclick="toggleReverseScheduleFields()">
                                    <label class="form-check-label">Would you like to reverse this schedule?</label>
                                </div>
                            </div>

                            @if($isProcessing)
                                <div class="alert alert-info" role="alert">
                                    <strong>Info!</strong> Task is in progress. You can't edit the schedule details.
                                </div>
                            @endif

                            <div class="row {{ $isReverse ? '' : 'd-none' }}" id="reverse_schedule_fields">
                                <div class="col-12">
                                    <fieldset class="scheduler-border">
                                        <legend class="scheduler-border">Reverse Schedule</legend>
                                        <div class="row">
                                            <div class="form-group mb-3 col-6">
                                                <label for="reverse_scheduled_at">Date and Time <span class="text-danger">*</span></label>
                                                <input type="datetime-local"
                                                       min="{{ date('Y-m-d\TH:i') }}"
                                                       value="{{ $reverseScheduledAt }}"
                                                       class="form-control scheduled_date is_reverse_schedule_required"
                                                       id="reverse_scheduled_at"
                                                       name="required_data[reverse][scheduled_at]" />
                                            </div>
                                            <div class="form-group mb-3 col-6">
                                                <label for="reverse_timezone">Timezone: <span class="text-danger">*</span></label>
                                                <select id="reverse_timezone" name="required_data[reverse][timezone]" class="form-control timezone is_reverse_schedule_required">
                                                    <option value="">Loading...</option>
                                                </select>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer p-3">
                    <button type="button" class="btn btn-outline-danger" id="attribute-cancel-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary bulk_module_js" id="attribute-save-button">Run Now</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function toggleScheduleFields() {
        const scheduleFields = $('#schedule_fields');
        if ($('#is_schedule').is(':checked')) {
            scheduleFields.removeClass('d-none');
            $(".is_schedule_required").prop('required' ,true);
            $('#attribute-save-button').html('Set Schedule');
        } else {
            scheduleFields.addClass('d-none');
            $('#reverse_schedule_fields').addClass('d-none');
            if ($('#sale_reverse').is(':checked')) {
                $('#sale_reverse').prop('checked', false);
            }
            $('#attribute-save-button').html('Run Now');

            $(".is_schedule_required").prop('required' ,false);

        }
    }

    function toggleReverseScheduleFields() {
        const reverseFields = $('#reverse_schedule_fields');
        if ($('#sale_reverse').is(':checked')) {
            $(".is_reverse_schedule_required").prop('required' ,true);
            reverseFields.removeClass('d-none');
        } else {
            $(".is_reverse_schedule_required").prop('required' ,false);
            reverseFields.addClass('d-none');
        }
    }

    $(document).ready(function() {
        toggleScheduleFields();
        toggleReverseScheduleFields();

        $('.bulk_update_price_type').change(function() {
            const updatePriceChecked = $('#update_price').is(':checked');
            const salePriceChecked = $('#sale_price').is(':checked');

            $('#sale_price_field').toggleClass('d-none', updatePriceChecked);
            if (updatePriceChecked) {
                $('#bulk_sale_price').val('');
            }

            $('#price_field').toggleClass('col-12', updatePriceChecked).toggleClass('col-6', salePriceChecked);
            $('#sale_price_field').toggleClass('col-6', salePriceChecked);
        });

        const timezones = moment.tz.names();
        const userTimezone = moment.tz.guess();
        // Set minimum value for date-time input
        const currentDatetime = moment().format('YYYY-MM-DDTHH:mm');

        $('.timezone').empty();
        $('.timezone').append(new Option("Select Timezone", ""));
        timezones.forEach(function(timezone) {
            $('.timezone').append(new Option(timezone, timezone));
        });

        // Store the PHP timezone value in a JS variable
        var selectedMainTimezone = "{{ $timezone }}";

        // Fallback to userTimezone if PHP variable is empty
        $('#timezone').val(selectedMainTimezone ? selectedMainTimezone : userTimezone);

        var selectedReverseTimezone = "{{ $reverseTimezone }}";
        $('#reverse_timezone').val(selectedReverseTimezone ? selectedReverseTimezone : userTimezone);

        // Set minimum value for date-time input
        if($("#edit_task").val() == "create"){
            $('.scheduled_date').attr('min', currentDatetime).val(currentDatetime);
        }
        else{
            $('#scheduled_at').attr('min', currentDatetime);
        }

        // Adjust reverse schedule minimum date based on main schedule date
        $("#scheduled_at").on("change", function () {
            var mainScheduleDate = $(this).val();
            if (mainScheduleDate) {
                if(mainScheduleDate < currentDatetime){
                    mainScheduleDate = currentDatetime;
                }
                $("#reverse_scheduled_at").attr("min", mainScheduleDate);
                var currentReverseDate = $("#reverse_scheduled_at").val();
                if (currentReverseDate && currentReverseDate < mainScheduleDate) {
                    $("#reverse_scheduled_at").val(mainScheduleDate);
                }
            }
        });

        // Trigger the change if scheduled_at is pre-filled
        if ($("#scheduled_at").val()) {
            $("#scheduled_at").trigger("change");
        }
    });
</script>

<script>
    $(document).ready(function() {
        // Function to validate input fields based on the provided regex patterns
        function validateInput(inputSelector, patterns, errorMessage, divId=null) {
            $(inputSelector).on('keyup', function() {
                var value = $(this).val().trim().toLowerCase();

                var isValid = patterns.some(function(pattern) {
                    return pattern.test(value);
                });

                if (!isValid) {
                    $(this).css('border', '2px solid red');
                    if(divId != null){
                        $("#" + divId).find('.text-danger').remove();
                        $("#" + divId).append(`<span class="text-danger small">${errorMessage}</span>`);
                    }
                    else{
                        $(this).next('.text-danger').remove();
                        $(this).after(`<span class="text-danger small">${errorMessage}</span>`);
                    }
                } else {
                    $(this).css('border', '1px solid #ccc');
                    $(this).next('.text-danger').remove();
                }
            });

            // Hide error message when the input is empty and loses focus
            $(inputSelector).on('blur', function() {
                // Check if the input field is empty
                if ($(this).val().trim() === '') {
                    // Reset the border style to normal
                    $(this).css('border', '1px solid #ccc');

                    // Remove any existing error message
                    if (divId != null) {
                        $("#" + divId).find('.text-danger').remove(); // Remove from divId if provided
                    } else {
                        $("#attribute-save-button").prop("disabled", false);
                        $(this).next('.text-danger').remove(); // Remove from the input field if no divId
                    }
                }
            });
        }

        // Define the valid patterns and error messages for both inputs
        var pricePatterns = [
            /^[+-]\d+$/,       // Matches whole numbers with mandatory + or - (e.g., +50, -50)
            /^[+-]\d+%$/,      // Matches percentage values with mandatory + or - (e.g., +50%, -50%)
        ];

        var salePricePatterns = [
            /^\d+$/,         // Matches whole numbers (e.g., 123, 456)
            /^\d+\.\d+$/     // Matches decimal values (e.g., 123.45, 0.678)
        ];

        // Call the validation function for both input fields
        validateInput('.apply_bulk_price_calculate_validation_js', pricePatterns, 'Enter a valid number or percentage (e.g., +10, +20%) for the price change.');
        // validateInput('.sale_price_input', salePricePatterns, 'Please match your values according to given examples', "sale_price_field_div_id");
    });

</script>
