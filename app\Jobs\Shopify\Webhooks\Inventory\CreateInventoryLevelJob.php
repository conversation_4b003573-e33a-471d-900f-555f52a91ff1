<?php

namespace App\Jobs\Shopify\Webhooks\Inventory;

use Illuminate\Bus\Queueable;
use App\Models\Product\Inventory;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use App\Models\Channel\ChannelLocation;
use App\Classes\Shopify\SyncInventories;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CreateInventoryLevelJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $channel;

    public $location;

    public $request;

    /**
     * Create a new job instance.
     */
    public function __construct($channel, $location, $request)
    {
        $this->channel = $channel;
        $this->location = $location;
        $this->request = $request;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        Log::channel('webhook')->info('Inventory webhook job started');
        // Log::channel('webhook')->info($this->request);
        // Log::channel('webhook')->info($this->channel);
        // Log::channel('webhook')->info($this->location);
        $inventory = Inventory::query()
        ->where([
            'organization_id' => $this->channel->organization_id,
            'store_connect_id' =>  $this->request["inventory_item_id"]
        ]);
    $inventory_data = $inventory->first();
    if ($this->location) {
        $inventory = $inventory->where("location_id", $this->location->id);
    }else{
        Log::channel('webhook')->error(['message' => 'Location not found in inventory webhook job']);
    }
    $inventory = $inventory->first();

        if($inventory){
            Log::channel('webhook')->info('Inventory found in inventory webhook job');
            if($inventory->available_quantity === $this->request["available"]){
                Log::channel('webhook')->info('Inventory quantity updated in inventory webhook job');
                return;
            }
        }


        if (!$inventory) {
            Log::channel('webhook')->info('Inventory not found in inventory webhook job so creating new inventory');
            if(!$inventory_data){
                Log::channel('webhook')->error(['message' => 'Product or variant not found in inventory webhook job']);
                return;
            }
            Log::channel('webhook')->error(['message' => 'Inventory not found']);
            $inventory = new Inventory();
            $inventory->organization_id = $this->channel->organization_id ?? null;
            $inventory->store_connect_id = $this->request["inventory_item_id"] ?? null;
            $inventory->location_id = $this->location->id ?? null;
            $inventory->product_id = $inventory_data->product_id ?? null;
            $inventory->variant_id = $inventory_data->variant_id ?? null;
            $inventory->store_type = 'shopify';
        }
        $inventory->available_quantity = $this->request["available"];
        $inventory->save();
        $channelLocation = ChannelLocation::find($inventory->location_id);
        Log::channel('webhook')->info('channelLocation');
        Log::channel('webhook')->info($channelLocation);

        if(!$channelLocation){
            Log::channel('webhook')->error(['message' => 'Channel location not found in inventory webhook job']);
            return;
        }

        $connectedChannels = ChannelLocation::where('location_id',$channelLocation->location_id)
            ->get();
        // Log::channel('webhook')->info('connectedChannels');
        // Log::channel('webhook')->info($connectedChannels);
        foreach ($connectedChannels as $key => $this->location) {
                $inventories= Inventory::query()
                ->where(['variant_id' => $inventory->variant_id, 'location_id' => $this->location->id])
                ->get();
                // Log::channel('webhook')->info('inventories');
                // Log::channel('webhook')->info($inventories);
                    foreach($inventories as $invy){
                        if($invy && $invy->location_id !== $inventory->location_id){
                         (new SyncInventories($invy, $this->location->channel_id))->setForWebhook();
                        }
                    }
            }
    }
}
