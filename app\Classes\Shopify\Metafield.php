<?php


namespace App\Classes\Shopify;

use App\Events\Product\ManageAttributes;
use Exception;
use Carbon\Carbon;
use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Models\Product\Attribute;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVersion;
use App\Models\Channel\ShopifyChannel;
use App\Models\Notification\ErrorLog;
use App\Models\Channel\ChannelProductStatus;
use App\Events\Product\CalculateScore;
use Ghazniali95\ShopifyConnector\App\Classes\Services\MetaFieldService;


class Metafield
{
    /**
     * @var int
     */
    public $channel_id ;

    /**
     * @var int
     */
    public $organization_id;
    /**
     * @var string
     */
    public $base_url ;
    /**
     * @var array
     */
    public $ids = array();

    /**
     * @var array
     */
    public $product_array = array();


    /**
     * @param int $channel_id
     * @param int $organization_id
     */
    public function __construct(int $channel_id, int $organization_id)
    {
        $this->channel_id = $channel_id;
        $this->organization_id = $organization_id;
        $this->base_url = (new ShopifyChannel())->set_base_url($this->channel_id);

    }

    /**
     *
     */
    public function getProductsId()
    {
        $channel_product_id = ChannelProduct::query()
            ->where('channel_id',$this->channel_id)
            ->get()
            ->pluck('id')
            ->toArray();

        $product_ids_response = ChannelProductStatus::query()
            ->whereIn('channel_product_id',$channel_product_id)
            ->where('type','shopify')
            ->whereNot('response',null)
            ->with('channel_product')
            ->get();
        $this->ids = $this->getIds($product_ids_response);

        return $this->ids;
    }

    /**
     * get the shopify product id and Apimio product id
     * @param $product_ids_response
     * @return array
     */
    public function getIds($product_ids_response): array
    {
        $ids = array();
        foreach($product_ids_response as $key=>$res){
            // $data = json_decode($res->response);
            $ids[$key]['shopify_product_id'] = $res->response->sync_id;
            $ids[$key]['product_id'] = $res->channel_product?$res->channel_product->product_id:0;
        }
        return $ids;
    }


    /**
     * fetch the meta fields from shopify
     * @return void
     */
    public function getMetaFields($ids) : void
    {
        try{
            //foreach ($this->ids as $ids){
                $shopify_product_id = $ids['shopify_product_id'];
                $this->product_array['id'] = $ids['product_id'];
                $this->product_array['shopify_id'] = $shopify_product_id;

                $MetaFieldService = new MetaFieldService($this->channel_id);
                $MetaFieldService->getAll(function($response) {
                    $this->store($response);
                },['limit' => 250 ,"metafield" => [
                    "owner_id" => $shopify_product_id ,
                    "owner_resource" => 'product'
                ]]);
           // }

        }catch(Exception $exception){
            return;
        //     $data = [
        //         'organization_id' => $this->organization_id,
        //         'description' => "There is an error while fetching meta fields.<br>  {$exception->getMessage()}",
        //         'type' => 'shopify',
        //         'link' => route('products.edit', $ids['product_id']),
        //         'link_text' => 'View Product',
        //         'status' => 'error',
        //     ];

        //     $error = new ErrorLog();
        //     $error->setData($data)->store(function ($error) {
        //         Log::channel('shopify')->info('Error in saving ErrorLogs.');
        //         Log::channel('shopify')->error($error);
        //     }, function () {
        //     });
            info('meta fields get from shopify error');
            info($exception);
        }
}

    /**
     * @param $metafield_data
     * @param $attribute
     * @return array|mixed
     */
    private function setValue($metafield_data , $attribute)
    {
            if($attribute->attribute_type_id == 8 ){
                $data = json_decode($metafield_data->value,true);
                return $data['value']  ;
            }
            elseif($attribute->attribute_type_id == 7){
                $data = json_decode($metafield_data->value,true);
                return [
                    'value'=>$data['value'],
                    'measurement'=>$data['unit'],
                ];
            }
            elseif($attribute->attribute_type_id == 5){
                $rules = json_decode($attribute->rules,true);
                if($rules['type'] == "date_and_time"){
                    return Carbon::parse($metafield_data->value)->format('Y-m-d\TH:i');
                }else{
                    return $metafield_data->value;
                }
            }
            elseif($attribute->attribute_type_id == 2){
                $rules = json_decode($attribute->rules,true);
                $data = json_decode($metafield_data->value,true);
                if($rules['type'] == "price"){
                    return [
                        'value'=>$data['amount'],
                        'measurement'=>$data['currency_code'],
                    ];
                }else{
                    return $metafield_data->value;
                }
            }
            elseif($attribute->attribute_type_id == 10){
                return $metafield_data->value?1:0;
            }
            else{
                return $metafield_data->value;
            }

    }

    /**
     * @param $value
     * @param $attribute
     * @return mixed|string
     */
    private function setListValue($value, $attribute){
        if($attribute->attribute_type_id == 8 ){
            return $value['value']  ;
        }
        elseif($attribute->attribute_type_id == 5){
            $rules = json_decode($attribute->rules,true);
            if($rules['type'] == "date_and_time"){
                return  Carbon::parse($value)->format('Y-m-d\TH:i');
            }else{
                return $value;
            }
        }
        else{
            return $value;
        }
    }

    /**
     * @param $values
     * @return array
     */
    private function setMeasurementListValue($values): array
    {

        $measurement= array();
        foreach ($values as $val){
            $measurement['measurement'][] = $val['unit'];
            $measurement['value'][]= $val['value'];
        }
        return $measurement;
    }

    /**
     * convert the shopify meta fields to Apimio array to save it in database
     * @param $meta_fields
     * @return array
     */
    public function convertToFamilyArray($meta_fields): array
    {

        $data1 = array();
        foreach ($meta_fields as $field){

            $handle = ($field->namespace == "global" )?"SEO": $field->namespace;

            if($field->namespace == "global"){
                if($field->key == "description_tag") {
                    $field->key = "seo_description";
                }
                elseif($field->key == "title_tag"){
                    $field->key = "seo_title";
                }
                elseif($field->key == "url_tag"){
                    $field->key = "seo_url";
                }
            }
            //get family

            $family_exist = Family::query()
                ->withoutGlobalScopes()
                ->where([
                    "organization_id"=> $this->organization_id,
                    "name"=>$handle
                ])->get()
                ->first();

            if(!$family_exist){
                Log::channel('shopify')->info('Family not exist For metafield saving');
                continue;
            }
            // get attribute
            $attribute_exist = Attribute::query()
                ->withoutGlobalScopes()
                ->where([
                    "organization_id"=> $this->organization_id,
                    "handle"=> $field->key,
                ])
                ->where('attribute_type_id','!=',13)
                ->whereHas('families',function($q) use ($family_exist){
                    $q->where('families.id',$family_exist->id);
                })
                ->first();



            // add value of attribute in an array
            if($family_exist && $attribute_exist) {

                $attr = $attribute_exist
                    ->families()
                    ->withoutGlobalScopes()
                    ->where("organization_id", $this->organization_id)
                    ->find($family_exist->id);
                if($attr){
                    $id = $attr->pivot->id;
                    if($handle == 'SEO'){
                        $data1[$id] = $this->setValue($field, $attribute_exist);
                    }else{
                        if($field->type !== 'file_reference' && $field->type !== 'list.file_reference' ){
                            $rules=json_decode($attribute_exist->rules,true);

                            //save values in array
                            if(isset($rules['value_type']) && $rules['value_type'] == 'single') {
                                $data1[$id] = $this->setValue($field, $attribute_exist);
                            }
                            else{
                                $values = json_decode($field->value,true);
                                if(isset($values)){
                                    if($attribute_exist->attribute_type_id == 7){
                                        $data1[$id] =  $this->setMeasurementListValue($values);
                                    }else{
                                        foreach($values as $value){
                                            $data1[$id][] =  $this->setListValue($value, $attribute_exist);
                                        }
                                    }
                                }
                            } // end of else condition
                        }

                    }
                }

            }

        }
        return $data1;

    }

    /**
     * call the store method to save the product in apimio database
     * @param $data
     * @return array|callable
     */
    public function store($data)
    {
        try {
            $attributes["attribute"] = $this->convertToFamilyArray($data);

            if(sizeof($attributes) > 0 ){
                $product = Product::query()->findOrFail($this->product_array['id']);
                $version_id =ChannelVersion::where('channel_id',$this->channel_id)->first()?->version_id;
                event(new ManageAttributes($product, $attributes, $version_id));
                event(new CalculateScore($product));

               // $this->product_array["attribute"]= $attributes;

    /*            if(!$version_id){
                    $version_id = $product->versions()->first()->id;
                }
                if (!empty($product->sku)) {
                    $this->product_array['sku'] = $product->sku;
                    $this->product_array['status'] = $product->status;
                    $this->product_array['version_id'] = $version_id;
                    $this->product_array['organization_id'] = $this->organization_id;
                    $this->product_array['not_change_update_date'] = true ;
                }

                $product_obj = new Product();
                return $product_obj->set_data($this->product_array)->store(
                    function ($errors){
                        info($errors);
                    },
                    function ($obj)  {
                    },
                    false
                );*/

            }
            return [];
        } catch (\Exception $exception) {
            $data = [
                'organization_id' => $this->organization_id,
                'description' => "There is an error while saving meta fields.<br>  {$exception->getMessage()}",
                'type' => 'shopify',
                'link' => "#",
                'link_text' => 'View Product',
                'status' => 'error',
            ];

            $error = new ErrorLog();
            $error->setData($data)->store(function ($error) {
                Log::channel('shopify')->info('Error in saving ErrorLogs.');
                Log::channel('shopify')->error($error);
            }, function () {
            });
        }

    }
}
