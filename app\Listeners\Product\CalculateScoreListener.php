<?php

namespace App\Listeners\Product;

use App\Events\Product\CalculateScore;
use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Family;
use App\Models\Product\ProductVersion;
use App\Models\Setting;
use App\Traits\Product\Rules\Validation;
use DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class CalculateScoreListener
{
    use Validation;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CalculateScore $event): void
    {
        try {
            $versions = [];
            $product = $event->model;
            $versionIds = [];
            foreach ($event->version_ids as $version) {
                if (array_key_exists('id', $version)) {
                    $versionIds[] = $version["id"];
                } else {
                    $versionIds[] = $version;
                }
            }
            if (empty($versionIds)) {
                $versions = $event->model->versions()->get();
            } else {
                $versions = $event->model->versions()->whereIn('versions.id', $versionIds)->get();
            }

            foreach ($versions as $version) {
                $attributeScores = $this->calculate_for_attributes($product, $version);
                $variantScores = $this->calculate_for_variants($product, $version);
                $specialAttributeScores = $this->calculate_for_special_attributes($event->model);

//                info("Attribute Scores: ", $attributeScores);
//                info("Variant Scores: ", $variantScores);
//                info("Special Attribute Scores: ", $specialAttributeScores);

                $validFields = count($attributeScores['valid']) + count($variantScores['valid']) + count($specialAttributeScores['valid']);
                $invalidFields = count($attributeScores['invalid']) + count($variantScores['invalid']) + count($specialAttributeScores['invalid']);

                $totalFields = $validFields + $invalidFields;
                $totalScore = $totalFields > 0 ? ($validFields / $totalFields) * 100 : 0;

                ProductVersion::query()
                    ->where("product_id", $product->id)
                    ->where("version_id", $version->id)
                    ->update([
                        "score" => floor($totalScore)
                    ]);
            }

        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

    }


    /**
     * Calculate the score for the all default and custom attributes of a product.
     * @param $product
     * @param $version
     * @return array|array[]
     */
    public function calculate_for_attributes($product, $version): array
    {
        $product_attributes_validation = [
            'invalid' => [],
            'valid' => [],
        ];

        // Get default family ids
        $default_family_ids = Family::where('organization_id', $product->organization_id)
                ->where("is_default", 1)
                ->pluck("id")
                ->toArray() ?? [];

        // Get all family ids attached to the product
        $family_ids = AttributeFamilyProductVersion::select('attribute_family_product_versions.family_id')
                ->where('version_id', $version->id)
                ->where('product_id', $product->id)
                ->distinct()
                ->pluck('family_id')->toArray() ?? [];

        // Get all attached families
        $attached_families = array_unique(array_merge($family_ids, $default_family_ids) ?? []);

        // Get all attached attributes ids
        $attached_attributes_ids = AttributeFamily::whereIn('family_id', $attached_families)->pluck('attribute_id')->toArray() ?? [];

        // Get all product attributes values
        $product_attributes_values = AttributeFamilyProductVersion::leftJoin('attributes', 'attribute_family_product_versions.attribute_id', '=', 'attributes.id')
            ->select('attribute_family_product_versions.attribute_id', 'attributes.attribute_type_id', 'attributes.rules', 'value', 'attribute_family_product_versions.unit','attribute_family_product_versions.family_id')
            ->where('version_id', $version->id)
            ->where('product_id', $product->id)
            ->get();

        // Group the product attributes by attribute_id
        $product_attributes_values = $product_attributes_values->groupBy('attribute_id')->map(function ($group) use (&$product_attributes_validation) {

            $first = $group->first();
            $values = $group->pluck('value')->toArray();
            $units = $group->pluck('unit')->toArray();

            $attribute = [
                'attribute_type_id' => $first->attribute_type_id,
                'rules' => $first->rules,
                'unit' => $units ?? [],
                'value' => $values ?? [],
                'attribute_id' => $first->attribute_id,
            ];

            $is_valid = $this->validate_attribute($attribute);

            if ($is_valid) {
                $product_attributes_validation['valid'][] = $attribute['attribute_id'];
            } else {
                $product_attributes_validation['invalid'][] = $attribute['attribute_id'];
            }
            return $attribute;
        })->values()->keyBy('attribute_id');

        // Get missing default attributes that are not present in product_attributes_values
        $missing_default_attributes = Attribute::where('organization_id', $product->organization_id)
        ->whereIn('id', $attached_attributes_ids)
            ->whereNotIn('id', $product_attributes_values->keys()->toArray())
            ->get();


        // Add missing default attributes with null value
        foreach ($missing_default_attributes as $missing_attribute) {
            $product_attributes_values[$missing_attribute->id] = [
                'attribute_type_id' => $missing_attribute->attribute_type_id,
                'rules' => $missing_attribute->rules,
                'value' => [null],
                'unit' => [null],
                'attribute_id' => $missing_attribute->id,
            ];
            $missing_attribute = $product_attributes_values[$missing_attribute->id];
            $is_valid = $this->validate_attribute($missing_attribute);
            if ($is_valid) {
                $product_attributes_validation['valid'][] = $missing_attribute['attribute_id'];
            } else {
                $product_attributes_validation['invalid'][] = $missing_attribute['attribute_id'];
            }
        }

        return $product_attributes_validation;
    }


    /**
     * @param $product
     * @param $version
     * @return array|array[]|null
     */
    public function calculate_for_variants($product, $version)
    {

        $product_variant_validation = [
            'invalid' => [],
            'valid' => [],
        ];


        $fieldMapping = [
            'name' => 'name',
            'sku' => 'sku',
            'price' => 'price',
            'compare_at_price' => 'compare_at_price',
            'barcode' => 'barcode',  // Map barcode to upc_barcode in settings
            'cost_price' => 'cost_price',
            'weight' => 'weight'
        ];
        $integer_fields = ['price', 'compare_at_price', 'cost_price', 'weight'];
        try {
            $variants = $product->variants()->where('version_id', $version->id)->get();
            if ($variants->isEmpty()) {
                return $product_variant_validation;
            }
            $totalVariantFields = count($variants) * count($fieldMapping);
            $validVariantFields = $totalVariantFields;
            $settings = $this->getSettings($product->organization_id);

            foreach ($variants as $variant) {
                foreach ($fieldMapping as $variantField => $settingsField) {
                    $settingValue = $settings[$settingsField] ?? 0;

                    if ($settingValue == 1 && $variant->$variantField == null) {
                        $product_variant_validation['invalid'][] = $settingsField;
                    }
                    elseif ($settingValue == 1 && in_array($variantField, $integer_fields) && !is_numeric($variant->$variantField)) {
                        $product_variant_validation['invalid'][] = $settingsField;
                    }
                    else {
                        $product_variant_validation['valid'][] = $settingsField;
                    }
                }
            }

            return $product_variant_validation;
        } catch (\Exception $e) {
            return null;
        }

    }

    /**
     * @param $product
     * @return array|null
     */
    public function calculate_for_special_attributes($product)
    {
        $product_special_validation = [
            'invalid' => [],
            'valid' => [],
        ];

        $product = $product->load('brands', 'categories', 'invites');

        $fieldMapping = [
            'sku' => 'sku',
            'brand' => 'brand',
            'category' => 'category',
            'vendor' => 'vendor'
        ];

        $fields = array_keys($fieldMapping);
        try {
            $specialFields = [
                'sku' => $product->sku,
                'brand' => $product->brands->first()->name ?? '',
                'category' => $product->categories->first()->name ?? '',
                'vendor' => $product->invites->first()->fname ?? ''
            ];

            $settings = $this->getSettings($product->organization_id);
            foreach ($specialFields as $field => $value) {
                $settingValue = $settings[$fieldMapping[$field]] ?? 1;
                if ($settingValue == 1 && $value == null) {
                    $product_special_validation['invalid'][] = $field;
                } else {
                    $product_special_validation['valid'][] = $field;
                }
            }

            return $product_special_validation;
        } catch (\Exception $e) {
            return null;
        }
    }


    /**
     * Gets settings for an organization.
     *
     * This function is used to avoid repeating the same query multiple times
     * in the class, saving time and reducing the number of database queries.
     */
    private function getSettings($organizationId)
    {
        return Setting::query()
            ->where('organization_id', $organizationId)
            ->get()
            ->pluck('value', 'key');
    }
}
