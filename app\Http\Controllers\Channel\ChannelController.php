<?php

namespace App\Http\Controllers\Channel;

use App\User;
use Carbon\Carbon;
use App\Traits\AWSSetting;
use Illuminate\Http\Request;
use App\Models\Product\Family;
use Illuminate\Queue\Jobs\Job;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Version;
use App\Jobs\Shopify\LocationJob;
use App\Models\Location\Location;
use App\Models\EventStatusHandler;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Jobs\TestLambdaBatchTestJob;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Models\Channel\ShopifyChannel;
use App\Models\Channel\ChannelLocation;
use App\Models\Product\AttributeFamily;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use App\Jobs\Shopify\FetchAllShopifyData;
use App\Models\Organization\Organization;
use Illuminate\Support\Facades\Validator;
use App\Services\Marketing\Hubspot\Hubspot;
use App\Models\Product\ProductShopifyMapping;
use App\Jobs\Shopify\SyncAllMetafieldsToStore;

class ChannelController extends Controller
{
  use AWSSetting;

    public static $WEBHOOK_LIST = [
        [
            'id' => null,
            'topic' => 'app/uninstalled',
            'address' => 'SHOPIFY_WEBHOOK_UNINSTALL_APP',
            'status' => 0,
            'disable' => true,
        ],
        [
            'id' => null,
            'topic' => 'products/create',
            'address' => 'SHOPIFY_WEBHOOK_PRODUCT_CREATE',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'products/delete',
            'address' => 'SHOPIFY_WEBHOOK_PRODUCT_DELETE',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'collections/create',
            'address' => 'SHOPIFY_WEBHOOK_CREATE_COLLECTION',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'collections/update',
            'address' => 'SHOPIFY_WEBHOOK_UPDATE_COLLECTION',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'products/update',
            'address' => 'SHOPIFY_WEBHOOK_PRODUCT_UPDATE',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'locations/create',
            'address' => 'SHOPIFY_WEBHOOK_CREATE_LOCATION',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'locations/update',
            'address' => 'SHOPIFY_WEBHOOK_UPDATE_LOCATION',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'locations/delete',
            'address' => 'SHOPIFY_WEBHOOK_DELETE_LOCATION',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'inventory_items/update',
            'address' => 'SHOPIFY_WEBHOOK_INVENTORY_UPDATE',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'inventory_levels/update',
            'address' => 'SHOPIFY_WEBHOOK_INVENTORY_LEVELS',
            'status' => 0,
            'disable' => false,
        ],
        [
            'id' => null,
            'topic' => 'collections/delete',
            'address' => 'SHOPIFY_WEBHOOK_DELETE_COLLECTION',
            'status' => 0,
            'disable' => false,
        ],
    ];

    public function __construct()
    {
// TODO: create a new ShopifyAuthController and put install shopify and redirect shopify in that

        $this->middleware(['auth', 'verified', 'activeOrganization'], ['except' => [
            'install_shopify', 'redirect_shopify','showShopifyRegistration','shopifyRegistration','syncProductAlert'
        ]]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'store')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $channel = new Channel();
        if ($request->has('q')) {
            $channel->filter(["name" => $request->get('q')]);
        }

        $data['channel'] = $channel->fetch();
        return view('channel.index', compact('data', 'request'));
    }
    //TODO: location for channel
//    public function create()
//    {
//        $versions = Version::all();
//        // Fetching locations not assigned to any channel
//        $unassigned_locations = Location::whereDoesntHave('channels')->get();
//        // Fetching locations that are assigned to any channel
//        $assigned_locations = Location::whereHas('channels')->get();
//        return view("channel.create", compact('versions', 'unassigned_locations', 'assigned_locations'));
//    }
    public function create()
    {
        if (Gate::denies('create-channel', \App\Models\Channel\Channel::query())) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $versions = Version::all();
        return view("channel.create", compact('versions'));
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $channel = new Channel();
        return $channel->set_user(Auth::user())->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        }, function ($channel) use ($request) {
//            TODO location for channel
            // Crud for location when creating channel if needed
//            if ($request->has('locations')) {
//                $locationIds = $request->get('locations');
//                // Validate the location IDs here...
//                $channel->locations()->attach($locationIds);
//            }
            $versionName = $channel->versions->first()->name ?? '';
            if (!$versionName) {
                Log::error('Failed to create location,No version found for channel ID: ' . $channel->id);
            }
            // creating a unique location for the new channel
            $locationName = $channel->name . ' Warehouse';
            $newLocation = new Location([
                'name' => $locationName,
                'organization_id' => Auth::user()->organization_id
            ]);
            $newLocation->save();

            // Attach location to the channel
            $channel->locations()->attach($newLocation->id);

            // Check if there are other locations to attach
            if ($request->has('locations')) {
                $locationIds = $request->get('locations');
                // Attach other locations if any
                $channel->locations()->attach($locationIds);
            }
            return redirect(route("channel.index"))->withSuccess("Store created successfully.");
        });
    }
    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $versions = Version::all();
        // Fetching locations not assigned to any channel
//        $unassigned_locations = Location::whereDoesntHave('channels')->get();
        $current_locations = Location::whereHas('channels',function($model) use($id){
            $model->where("channel_id" ,$id);
        })->get();
        // If no current locations found for the channel, log an error.
        if ($current_locations->isEmpty()) {
            Log::error("No locations found for channel with ID: {$id}");
        }
        // Fetching locations that are assigned to any channel
//        $assigned_locations = Location::whereHas('channels',function($model) use($id){
//            $model->where("channel_id","!=",$id);
//        })->get();
        // Fetch the channel you want to edit
        $channel = Channel::findOrFail($id);
        $location_ids = ChannelLocation::where('channel_id',$id)->distinct('location_id')->pluck('location_id')->toArray();
        $apimio_locations = Location::query()
            ->where(function($query) use ($id,$location_ids){
                $query->whereHas('channelLocation' , function($query) use ($id,$location_ids) {
                    $query->whereNot('channel_id',$id);
                    //->whereNotIn('location_id',$location_ids);
                });
                //->orWhereDoesntHave('channelLocation');
            })
            ->where('organization_id',auth()->user()->organization_id)->distinct('name')->get();

        $store_locations = ChannelLocation::with('location')->where('channel_id',$id)->get();

        if(isset($channel->shopify_channel->access_token)){
            // Merge static and API webhooks
            $webhooks = $this->mergeWebhooks(self::$WEBHOOK_LIST, $this->getAllWebhooks($id));
        }else{
            $webhooks = self::$WEBHOOK_LIST;
        }
        return view("channel.shopify.edit", compact('channel', 'versions', 'current_locations','apimio_locations','store_locations','webhooks'));
    }
    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $channel = new Channel();
        return $channel->set_id($id)->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        },  function () {
//            function ($channel) use ($request) {
//            // Validate locations and versions before attaching
//            if ($request->has('locations')) {
////                $locationIds = $request->get('locations');
//                $locationNames = $request->get('locations');
//                $locationIds = Location::query()->where('name', $locationNames)->pluck('id')->toArray();
//                // Validate the location IDs here...
//                $channel->locations()->sync($locationIds);
//            }
            return back()->withSuccess("Store updated successfully.");
        });
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function shopify()
    {
        return view("channel.shopify.view");
    }

    public function install_shopify(Request $request)
    {
        Session::put('sync_product', $request->get('sync_product'));

        if($request->has('sync_metafields')){
            Session::put('sync_metafields', $request->get('sync_metafields'));
        }

        if($request->has('sync_product') && $request->sync_product == 'yes')
        {
            $event = new EventStatusHandler();
            $event->set_data([
                'organization_id'=>auth()->user()->organization_id,
                'listener_name'=>'FetchShopifyProduct'
            ])->store();
        }
        if($request->has("hmac")) {
            Session::put('should_bill', true);
        }
        if($request->has('is_shopify_update')){
            Session::put([
                'type' => 'previous_user',
                'update_channel_id' => $request->channel_id
            ]);
        }else{
            Session::put('channel_id', $request->channel_id);
        }
        if($request->has("add_new_channel")){
            Session::put('add_new_channel', $request->add_new_channel);
        }
        Session::save();
        $shopifyChannel = new ShopifyChannel();
        return $shopifyChannel->set_data($request->all())
            ->install(
            // when error
                function ($errors) {
                    return back()->withErrors(["main" => $errors->all()]);
                }
            );
    }

    public function redirect_shopify(Request $request)
    {
        $shopifyChannel = new ShopifyChannel();
        if(session()->has("type") && session()->get("type") == "previous_user"){
            return  $shopifyChannel->set_data($request->all())->getPreviousUsersShopifyLocation(// when success
                function ($msg) {
                    Session::forget('type');
                    Session::forget('update_channel_id');
                    return redirect(route("dashboard"))->withSuccess($msg);
                },
                // when error
                function ($errors) {
                    Session::forget('type');
                    Session::forget('update_channel_id');
                    return redirect(route("dashboard"))->withErrors($errors);
                });
        }
        \Log::info("Start the shopify work");

        return $shopifyChannel->set_data($request->all())->redirect(
        // when success
            function ($obj) {

                if (session()->has("should_bill")) {
                    session()->remove("should_bill");
                    // return redirect(route("channel.shopify.bill", ["shopify_channels_id" => $obj->get_shopify_channel()["id"]]));
                    // dd(route("billing"));
                    return redirect()->route('dashboard')->withSuccess("Shopify connected successfully.");
                }
                if (session()->has('sync_product')) {
                    if (session()->get('sync_product') == 'yes') { // if user selects shopify store after creation of organization
                        if(env('CUSTOM_QUEUE_WORKER') == 'local'){
                            event(new \App\Events\PostBillingEvent(auth()->user()->organization_id, auth()->id())); //fetching user products if he selects sync products
                        }else{
                            $jobs = [
                                new FetchAllShopifyData(['channel_id'=>$obj->channel_id,'organization_id'=>auth()->user()->organization_id, 'user_id'=>auth()->id()]),
                            ];
                            $this->dispatchJobsToFifoQueue($jobs, auth()->user()->organization_id.'-main',auth()->user()->organization_id);
                        }
                        return redirect()->route('dashboard')->withSuccess("Shopify connected successfully."); //redirecting to dashboard
                    }
                }

                if(session()->has('add_new_channel')){
                    $event = new EventStatusHandler();
                    $event->set_data([
                        'organization_id' => auth()->user()->organization_id,
                        'listener_name' => 'FetchShopifyProduct'
                    ])->store();
                    if(env('CUSTOM_QUEUE_WORKER') == 'local'){
                        event(new \App\Events\FetchShopifyProductEvent(auth()->user()->organization_id, auth()->id(),$obj->get_shopify_channel()['channel_id']));
                    }else{
                        $jobs = [
                            new FetchAllShopifyData(['channel_id'=>$obj->get_shopify_channel()['channel_id'],'organization_id'=>auth()->user()->organization_id, 'user_id'=>auth()->id()]),
                        ];
                        $this->dispatchJobsToFifoQueue($jobs, auth()->user()->organization_id.'-main',auth()->user()->organization_id);
                    }
                    Session::forget('add_new_channel');

                }

                if(session()->has('sync_metafields')){
                    Session::forget("sync_metafields");
                    $shopifyData = [
                        'user_id'=>auth()->id(),
                        'organization_id'=>auth()->user()->organization_id,
                        'channel_id'=>$obj->get_shopify_channel()['channel_id'],
                    ];
                    dispatch(new SyncAllMetafieldsToStore($shopifyData));
                }

                // if(session()->has('add_new_channel')){
                //     dispatch(new LocationJob((object) $shopifyData));
                //     Session::forget('add_new_channel');
                // }

                return redirect(route("dashboard"))->withSuccess("Shopify connected successfully.");
            },
            // when error
            function ($errors) {
                return redirect(route("dashboard"))->withErrors($errors);
            }
        );
    }

    public function shopify_bill($shopify_channels_id)
    {
        // dd(3);
        $shopifyChannel = new ShopifyChannel();
        if (!$shopifyChannel->is_billed()) {
            //if user is not subscribed
            return view("channel.shopify.bill.index", ["shopify_channels_id" => $shopify_channels_id, 'check_trial' => $shopifyChannel->on_trial(Auth::user()->organization_id)]);
        }
        return redirect(route("channel.shopify.password.create"));
    }

    public function store_shopify_bill(Request $request)
    {
        // dd(4);
        $shopifyChannel = new ShopifyChannel();

        if(!$request->shopify_channel_id){
            return redirect(route('channel.index'))->withErrors(['main' => "connect your store with shopify"]);
        }
        /*
         * Check if plan is of $0 then do not call shopify
         * payment it does not authorize 0 payment.
        */

        if ($shopifyChannel->set_data($request->all())->is_plan_price_is_zero()) {
            return Redirect::route("set.shopify.product")->withSuccess("Shopify Connected Successfully.");
        } else {
            return $shopifyChannel->bill(
            // when error
                function ($errors) {
                    return back()->withErrors($errors);
                }
            );
        }
    }

    public function redirect_shopify_bill(Request $request)
    {
        // dd(4);
        $shopifyChannel = new ShopifyChannel();
        return $shopifyChannel->set_data($request->all())->redirect_bill(
        // when error
            function ($validation) {
                return back()->withErrors($validation->errors());
            },
            // when success
            function () {
                if(!auth()->user()->password) {
                    return redirect(route("set.shopify.product"))->withSuccess("Shopify store connected successfully.");
                }
                return redirect(route("dashboard"))->withSuccess("Shopify subscription changed successfully.");
            }
        );
    }

    public function shopify_fetch_all_products(string $channel_id)
    {
        $event = new EventStatusHandler();
        $event->set_data([
            'organization_id' => auth()->user()->organization_id,
            'listener_name' => 'FetchShopifyProduct'
        ])->store();

        if(env('CUSTOM_QUEUE_WORKER') == 'local'){
            event(new \App\Events\FetchShopifyProductEvent(auth()->user()->organization_id, auth()->id(),$channel_id));
        }else{
            $jobs = [
                new FetchAllShopifyData(['channel_id'=>$channel_id,'organization_id'=>auth()->user()->organization_id, 'user_id'=>auth()->id()]),
            ];
            $this->dispatchJobsToFifoQueue($jobs, auth()->user()->organization_id.'-main',auth()->user()->organization_id);
        }
        return redirect(route("dashboard"))->withSuccess("Shopify Product Fetch Queue Initiated Successfully.");
    }

    private function getAllWebhooks($channel_id)
    {
        try {
            $webhooks = Cache::get('shopify_webhooks_' . $channel_id);
            if(empty($webhooks)){
                $store = ShopifyChannel::where("channel_id", $channel_id)
                    ->select("access_token", "shop", "shop_id")
                    ->first(); // Use first() instead of get() to get a single record

                if ($store) {
                    if (isset($store->shop) && isset($store->access_token)) {

                        $response = Http::retry(1, 1000)->withHeaders([
                            'Content-Type' => 'application/json'
                        ])
                            ->get("https://" . env("SHOPIFY_API_KEY") . ":" . $store->access_token . "@" . $store->shop . "/admin/api/2024-01/webhooks.json");

                        if ($response->successful()) {
                            $webhooks = $response->json()['webhooks'];
                            // Save webhooks in the cache for 10 minutes
                            Cache::put('shopify_webhooks_' . $channel_id, $webhooks, now()->addMinutes(10));
                        } else {
                            \Log::channel("webhook")->error("Issue with the webhook when fetching");
                        }
                    }
                }

            }

            return $webhooks;

        } catch (\Exception $e) {
            \Log::channel("webhook")->error($e->getMessage());
        }
    }

    public function unInstallWebhooks(string $webhook_id,string $channel_id){
        try {
            Cache::forget('shopify_webhooks_' . $channel_id);
            $store = ShopifyChannel::where("channel_id", $channel_id)
                ->select("access_token", "shop", "shop_id")
                ->first();

            if($store){
                $w_response = Http::retry(1, 1000)->withHeaders([
                    'Content-Type' => 'application/json'
                ])
                    ->delete("https://" . env("SHOPIFY_API_KEY") . ":" . $store->access_token . "@" . $store->shop . "/admin/api/2024-01/webhooks/" . $webhook_id . ".json");
                if ($w_response->successful()) {
                    \Log::channel("webhook")->error(["webhook deleted successfully"]);
                } else {
                    \Log::channel("webhook")->error(["issue with the webhook when deleting"]);
                }
            }

            return redirect()->back()->withSuccess("webhook deleted successfully");

        } catch (\Exception $e) {
            \Log::channel("webhook")->error([$e]);
        }
    }

    public function register_webhook(Request $request){

        $store = ShopifyChannel::where("channel_id", $request->channel_id)
            ->select("access_token", "shop", "shop_id")
            ->first();
        if(isset($store) && isset($store->access_token) && isset($store->shop )){
            $response = Http::retry(1, 50)->post("https://" . env("SHOPIFY_API_KEY") . ":" . $store->access_token . "@" . $store->shop . "/admin/api/2024-01/webhooks.json", [
                "webhook" => [
                    "topic" => $request->topic,
                    "address" => env($request->address),
                    "format" => "json"
                ]
            ]);
            if($response->successful()) {
                Cache::forget('shopify_webhooks_' . $request->channel_id);
                $json =  $response->json();
                Log::channel('webhook')->info([$json]);
            }
            if($response->failed()) {
                $json = $response->json();
                Log::error([$json]);
            }
            return true;
        }
        return false;
    }

    private function mergeWebhooks($staticWebhooks, $apiWebhooks)
    {
        if(!empty($apiWebhooks)){
            $apiWebhookTopics = array_column($apiWebhooks, 'topic');
            $apiWebhooksAssoc = array_column($apiWebhooks, null, 'topic');

            foreach ($staticWebhooks as &$staticWebhook) {
                if (isset($apiWebhooksAssoc[$staticWebhook['topic']])) {
                    $staticWebhook['status'] = 1;
                    $staticWebhook['id'] = $apiWebhooksAssoc[$staticWebhook['topic']]['id'];
                }
            }
        }
        return $staticWebhooks;
    }

    protected function validator(array $data)
    {
        $validator = Validator::make($data, [
            'full_name' => ['required', 'string', 'max:75'],
            'phone' => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/|min:10|max:20',
            'email' => ['required', 'string', 'email', 'max:255'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ], [
            'full_name.required' => 'full name name cannot be empty',
            'full_name.min' => 'First name should not be less than 3 characters',
            'full_name.max' => 'First name should not be greater than 255 characters',
            'email.required' => 'Email cannot be empty',
            'email.max' => 'Email should not be greater than 255 characters',
            'phone.max' => 'Phone number should not be greater than 20 digits',
            'phone.min' => 'Phone number should not be less than 10 digits'
        ]);
        if ($validator->fails())
            return $validator;
        $validator->after(function ($validator) {
            $data = $validator->getData();
            if (User::where('email', $data["email"])->whereNotNull('password')->count() > 0)
                $validator->errors()->add('email', 'The email has already been taken.');

        });
        return $validator;

    }

    public function shopifyRegistration(Request $request)
    {
        // dd($request->shop_data);
        DB::beginTransaction();
            $data = $request->only(['full_name','phone','email','password','password_confirmation','ip']);
            $shop_data = $request->shop_data;
            $this->validator($data)->validate();
            if(!isset($data['phone'])){
                return back()->withErrors(['phone'=> 'Phone number is required.']);
            }

            $user = new User();
            $user->fname = $data['full_name'];
            $user->lname = '';
            $user->email = $data['email'];
            $user->phone = $data['phone'];
            $user->shopify_shop_id = $shop_data["shopify_shop_id"];
            $user->email_verified_at = Carbon::now();
            $user->password = Hash::make($data['password']);
            $user->save();
            $this->createOrganization($user,$shop_data);
            DB::commit();

            auth()->login($user, true);

            $shopify_channel = ShopifyChannel::where("shop_id", $shop_data["shopify_shop_id"])->first();

            if(!$shopify_channel) {
                $shopify_channel = new ShopifyChannel();
                $channel = new Channel();
                if(Session::has('channel_id')){
                    $channel = $channel->findOrFail(Session::get('channel_id'));
                }else{
                    $channel = $channel->doesntHave("shopify_channels")->get()->first();
                }
                if(!$channel) {
                    if($user->organization_id){
                        if (Gate::denies('create-channel', \App\Models\Channel\Channel::query())) {
                            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to create new store"]);
                        }
                    }
                    $channel = new Channel();
                    if(!$user->organization_id){
                        if($user->organizations->first()){
                            $org = $user->organizations->first();
                            $user->organizations->first()->set_active_organization_by_id($org->id);
                        }else{
                            $this->createOrganization($user,$shop_data);
                        }
                    }
                    $channel->organization_id = $user->organization_id;
                    $channel->name = $shop_data["name"];
                    $channel->type = "shopify";
                    $channel->save();
                }

                $shopify_channel->channel_id = $channel->id;
                $shopify_channel->access_token = $shop_data["access_token"];
                $shopify_channel->shop = $shop_data["shop"];
                $shopify_channel->shop_id = $shop_data["shopify_shop_id"];
                $shopify_channel->save();

            }
            $shop_data['channel_id'] = $shopify_channel->channel_id;
            if($request->schedule_demo){
                // if(App::environment("production")) {
                    $hubspot = new Hubspot($user);
                    $hubspot->create();
                // }
            }




            return redirect()->route('shopify.fetch.products',['shop_data'=> $shop_data]);
    }

    public function createOrganization($user,$shop_data){

        //set data for saving organization
        preg_match('/\{\{(.*?)\}\}/', $shop_data["money_in_emails_format"], $matches);
        $separator = $matches[1];
        if($separator == "amount_with_comma_separator"){
            $separator = ",";
        }else{
            $separator = ".";
        }

        $org_data = [
            'name'=> $shop_data["name"],
            'region'=> $shop_data["country_code"] ?? 'AM',
            'units'=> $shop_data["weight_unit"] ?? 'SI',
            'currency'=> $shop_data["currency"] ?? 'USD',
            'separator'=> $separator ?? '.',
            'trail_user_days'=> 14,
            'shop_id'=> $shop_data["shopify_shop_id"],
        ];


        //save organization
         (new Organization())->set_data($org_data)
            ->set_user($user)
            ->store(
                function($error){
                    Log::info('shopify creating organization error');
                    Log::error($error);
                },function($obj){
                    return  $obj;
                }
            );

            $webhooks = [
                [
                    'topic' => 'app/uninstalled',
                    'address' => 'SHOPIFY_WEBHOOK_UNINSTALL_APP',
                    "format" => "json"
                ],
                [
                    'topic' => 'products/create',
                    'address' => 'SHOPIFY_WEBHOOK_PRODUCT_CREATE',
                    "format" => "json"
                ],
                [
                    'topic' => 'products/delete',
                    'address' => 'SHOPIFY_WEBHOOK_PRODUCT_DELETE',
                    "format" => "json"
                ],
                [
                    'topic' => 'collections/create',
                    'address' => 'SHOPIFY_WEBHOOK_CREATE_COLLECTION',
                    "format" => "json"
                ],
                [
                    'topic' => 'collections/update',
                    'address' => 'SHOPIFY_WEBHOOK_UPDATE_COLLECTION',
                    "format" => "json"
                ],
                [
                    'topic' => 'products/update',
                    'address' => 'SHOPIFY_WEBHOOK_PRODUCT_UPDATE',
                    "format" => "json"
                ],
                [
                    'topic' => 'locations/create',
                    'address' => 'SHOPIFY_WEBHOOK_CREATE_LOCATION',
                    "format" => "json"
                ],
                [
                    'topic' => 'locations/update',
                    'address' => 'SHOPIFY_WEBHOOK_UPDATE_LOCATION',
                    "format" => "json"
                ],
                [
                    'topic' => 'locations/delete',
                    'address' => 'SHOPIFY_WEBHOOK_DELETE_LOCATION',
                    "format" => "json"
                ],
                [
                    'topic' => 'inventory_levels/update',
                    'address' => 'SHOPIFY_WEBHOOK_INVENTORY_LEVELS',
                    "format" => "json"
                ],
                [
                    'topic' => 'collections/delete',
                    'address' => 'SHOPIFY_WEBHOOK_DELETE_COLLECTION',
                    "format" => "json"
                ],
            ];

            $this->register_webhooks($shop_data,$webhooks);
    }

    public function register_webhooks($store,$webhooks) :void
    {
        foreach ($webhooks as $webhook) {
            $response = Http::retry(1, 50)->post("https://" . env("SHOPIFY_API_KEY") . ":" . $store['access_token'] . "@" . $store['shop'] . "/admin/api/2024-01/"."webhooks.json", [
                "webhook" => $webhook
            ]);
            if($response->successful()) {
                $json =  $response->json();
                Log::channel('webhook')->info([$json]);
            }
            if ($response->failed()) {
                $json =  $response->json();
                // Handle the failed request if needed
                Log::info("webhook error");
                Log::error([$json]);
            }
        }
    }

    public function showShopifyRegistration(Request $request)
    {
        $shop_data = $request->shop;
        return view('channel.shopify.install.create', compact('shop_data'));
    }

    public function syncProductAlert(Request $request)
    {
        $shop_data = $request->shop_data;
        $user = auth()->user();
        $organization = Organization::where('id',$user->organization_id)->first();
        unset($shop_data['access_token']);
        unset($shop_data['shopify_shop_id']);
        return view('channel.shopify.install.welcome', compact('shop_data','user','organization'));
    }


}
